<?php

namespace App\Filament\Resources\Teams\Schemas;

use Filament\Infolists\Components\ColorEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;

class TeamInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Team Identity')
                    ->description('Basic team information and identification')
                    ->schema([
                        ImageEntry::make('logo')
                            ->label('')
                            ->disk('public')
                            ->circular()
                            ->columnSpan(1),
                        TextEntry::make('name')
                            ->label('Team Name')
                            ->weight(FontWeight::Bold)
                            ->size('xl')
                            ->columnSpan(2),
                        TextEntry::make('name_ar')
                            ->label('Team Name (Arabic)')
                            ->weight(FontWeight::Medium)
                            ->placeholder('Not specified')
                            ->columnSpan(2),
                        TextEntry::make('short_name')
                            ->label('Short Name')
                            ->weight(FontWeight::Medium)
                            ->columnSpan(1),
                        TextEntry::make('code_name')
                            ->label('Team Code')
                            ->badge()
                            ->color('primary')
                            ->columnSpan(1),
                    ])
                    ->columnSpan('full')
                    ->columns(3)
                    ->extraAttributes([
                        'class' => 'bg-gradient-to-br from-white to-gray-50 border-2 border-gray-200 rounded-xl shadow-lg p-6',
                    ]),

                Section::make('Kit Colors')
                    ->description('Team uniform and goalkeeper kit colors')
                    ->schema([
                        ColorEntry::make('shirt')
                            ->label('Home Kit')
                            ->tooltip('Primary team kit color'),
                        ColorEntry::make('gk_shirt')
                            ->label('Goalkeeper Kit')
                            ->tooltip('Goalkeeper uniform color'),
                    ])
                    ->columns(2),

                Section::make('Club Information')
                    ->description('Additional team details and history')
                    ->schema([
                        TextEntry::make('president')
                            ->label('Club President')
                            ->placeholder('Not specified')
                            ->icon('heroicon-o-user-circle'),
                        TextEntry::make('founded_at')
                            ->label('Founded')
                            ->date('Y')
                            ->placeholder('Unknown')
                            ->icon('heroicon-o-calendar-days'),
                        TextEntry::make('homeStadium')
                            ->label('Home Stadium')
                            ->placeholder('Not specified')
                            ->icon('heroicon-o-map-pin')
                            ->state(function ($record) {
                                $homeStadium = $record->homeStadium()->first();

                                return $homeStadium ? $homeStadium->name : 'Not specified';
                            }),
                        TextEntry::make('otherStadiumsCount')
                            ->label('Other Stadiums')
                            ->state(function ($record) {
                                return $record->stadiums()->wherePivot('is_home', false)->count();
                            })
                            ->placeholder('0')
                            ->icon('heroicon-o-building-library'),
                    ])
                    ->columns(2),

                Section::make('System Information')
                    ->description('Record timestamps and metadata')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime('M j, Y g:i A')
                            ->icon('heroicon-o-plus-circle'),
                        TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime('M j, Y g:i A')
                            ->since()
                            ->icon('heroicon-o-pencil-square'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}
