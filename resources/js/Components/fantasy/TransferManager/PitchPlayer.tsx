import React, { <PERSON> } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";

const PitchPlayer: FC<{
    player: TransferPlayer;
    position: { x: number; y: number };
    onClick: (player: TransferPlayer) => void;
    onInfoClick: (player: TransferPlayer) => void;
    isSelected: boolean;
}> = ({ player, position, onClick, onInfoClick, isSelected }) => {
    const { t } = useTranslation();
    const isPlaceholder =
        typeof player.id === "string" && player.id.startsWith("placeholder");
    const playerStyle: React.CSSProperties = {
        position: "absolute",
        left: `${position.x}%`,
        top: `${position.y}%`,
        transform: "translate(-50%, -50%)",
        cursor: "pointer",
        textAlign: "center",
        width: "80px",
        transition: "all 0.3s ease",
        border: isSelected ? "3px solid #68d391" : "3px solid transparent",
        borderRadius: "8px",
        padding: "2px",
    };

    return (
        <div
            style={playerStyle}
            onClick={() => onClick(player)}
            className="flex flex-col items-center group"
        >
            <div className={`relative w-full`}>
                <div
                    className={`w-12 h-12 mx-auto rounded-full flex items-center justify-center text-white font-bold shadow-lg ${
                        isPlaceholder
                            ? "bg-gray-600 border-2 border-dashed"
                            : player.position === "GK"
                            ? "bg-yellow-500"
                            : "bg-red-600"
                    }`}
                >
                    {isPlaceholder ? "+" : player.position}
                </div>
                <div
                    className={`text-xs font-semibold ${
                        isPlaceholder
                            ? "text-gray-400"
                            : "bg-black bg-opacity-70 text-white"
                    } px-2 py-1 rounded mt-1 whitespace-nowrap`}
                >
                    {player.name}
                </div>
                {!isPlaceholder && (
                    <div className="text-xs font-bold text-white bg-green-600 px-2 rounded-full mt-1">
                        {player.price.toFixed(1)}
                    </div>
                )}
                {!isPlaceholder && (
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            onInfoClick(player);
                        }}
                        className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full h-6 w-6 flex items-center justify-center text-xs font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-blue-700"
                        aria-label={t("transferManager.playerActions.info", {
                            name: player.name,
                        })}
                    >
                        i
                    </button>
                )}
            </div>
        </div>
    );
};

export default PitchPlayer;