<?php

namespace App\Http\Controllers;

use App\Models\Player;
use App\Services\FantasyContextService;
use App\Services\PlayerDataService;
use App\Services\SquadManagementService;
use App\Services\TransferService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class TransferController extends Controller
{
    protected FantasyContextService $contextService;

    protected PlayerDataService $playerDataService;

    protected SquadManagementService $squadService;

    protected TransferService $transferService;

    public function __construct(
        FantasyContextService $contextService,
        PlayerDataService $playerDataService,
        SquadManagementService $squadService,
        TransferService $transferService
    ) {
        $this->contextService = $contextService;
        $this->playerDataService = $playerDataService;
        $this->squadService = $squadService;
        $this->transferService = $transferService;
    }

    /**
     * Show the transfers page with current squad data and available players
     */
    public function index(Request $request)
    {
        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return redirect()->route('home')->with('error', $contextResult['error']);
        }

        $context = $contextResult['data'];
        $competition = $context['competition'];
        $currentGameweek = $context['currentGameweek'];
        $currentSeason = $context['currentSeason'];

        // Get user's fantasy team for current season
        $fantasyTeam = $this->contextService->getUserFantasyTeam($request);

        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create')
                ->with('error', 'Please create your fantasy team first.');
        }

        // Get squad players using service
        $squadPlayers = $this->playerDataService->getSquadPlayersData($fantasyTeam->id, $currentGameweek);

        // Get available players using service
        $currentSquadPlayerIds = $squadPlayers->pluck('id')->toArray();
        $availablePlayers = $this->playerDataService->getAvailablePlayersData($request, $currentSquadPlayerIds, $currentGameweek, $competition);

        // Calculate team stats
        $squadValue = $squadPlayers->sum('price');
        $budget = $fantasyTeam->budget; // Use raw budget value from database

        // Get number of free transfers for this gameweek
        $freeTransfers = $currentGameweek->free_transfers ?? 1;

        // Get starting players from the squad data to avoid an extra query
        $squadFantasyPlayers = $this->playerDataService->getSquadPlayers($fantasyTeam->id, $currentGameweek);
        $startingPlayerIds = $squadFantasyPlayers->filter(function ($fp) {
            return $fp->fantasyTeamLineups->isNotEmpty() && $fp->fantasyTeamLineups->first()->position === 'starting';
        })->pluck('player_id')->toArray();

        return Inertia::render('Transfers', [
            'competition' => $competition,
            'season' => $currentSeason,
            'gameweek' => $currentGameweek,
            'fantasyTeam' => $fantasyTeam,
            'availablePlayers' => $availablePlayers,
            'currentSquad' => $squadPlayers,
            'budget' => $budget,
            'freeTransfers' => $freeTransfers,
            'startingPlayerIds' => $startingPlayerIds,
        ]);
    }

    /**
     * Process player transfers
     */
    public function processTransfers(Request $request)
    {
        // In transfer mode, we only validate squad changes, not captain/vice-captain
        $validated = $request->validate([
            'selected_players' => 'required|array|size:15',
            'selected_players.*' => 'required|exists:players,id',
            'lineup_players' => 'required|array|size:11',
            'lineup_players.*' => 'required|exists:players,id',
            'transfers_made' => 'required|integer|min:0',
        ]);

        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return response()->json(['error' => $contextResult['error']], 400);
        }
        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];

        // Get user's fantasy team
        $fantasyTeam = $this->contextService->getUserFantasyTeam($request);

        if (! $fantasyTeam) {
            return response()->json(['error' => 'Fantasy team not found'], 404);
        }

        // Get current squad from the service to leverage caching
        $currentSquad = $this->playerDataService->getSquadPlayers($fantasyTeam->id, $currentGameweek);

        $currentSquadPlayerIds = $currentSquad->pluck('player_id')->toArray();
        $newSquadPlayerIds = $validated['selected_players'];

        // Calculate players added and removed
        $playersAdded = array_values(array_diff($newSquadPlayerIds, $currentSquadPlayerIds));
        $playersRemoved = array_values(array_diff($currentSquadPlayerIds, $newSquadPlayerIds));

        // Check if the number of transfers matches
        if (count($playersAdded) !== count($playersRemoved)) {
            return response()->json(['error' => 'Transfer count mismatch'], 400);
        }

        // Validate transfers count
        $freeTransfers = $currentGameweek->free_transfers ?? 1;
        if ($validated['transfers_made'] > count($playersAdded)) {
            return response()->json(['error' => 'Invalid transfers count'], 400);
        }

        // Get existing captain and vice-captain from the squad to preserve them
        $captain = $currentSquad->firstWhere('is_captain', true);
        $viceCaptain = $currentSquad->firstWhere('is_vice_captain', true);

        $currentCaptainId = $captain ? $captain->player_id : null;
        $currentViceCaptainId = $viceCaptain ? $viceCaptain->player_id : null;

        // Add preserved captain/vice-captain to validated data
        $validated['captain_id'] = $currentCaptainId;
        $validated['vice_captain_id'] = $currentViceCaptainId;

        // Calculate squad value and transfer cost
        $transferPoints = max(0, count($playersAdded) - $freeTransfers) * 4; // 4 points per transfer above free limit

        // Validate team budget
        $newPlayers = Player::whereIn('id', $newSquadPlayerIds)
            ->with(['marketValues' => function ($query) use ($currentGameweek) {
                $query->where('gameweek_id', $currentGameweek->id);
            }])
            ->get();

        $newSquadValue = $newPlayers->sum(function ($player) use ($currentGameweek) {
            return $player->getMarketValueForGameweek($currentGameweek->id) ?? 0;
        });

        if ($newSquadValue > $fantasyTeam->budget) {
            return response()->json(['error' => 'Not enough budget for these transfers'], 400);
        }

        try {
            // Use transfer service to handle comprehensive transfer processing
            // This includes squad updates, transfer recording, and penalty tracking
            $this->transferService->processTransfers(
                $fantasyTeam,
                $currentGameweek,
                $validated,
                $newSquadPlayerIds,
                $playersAdded,
                $playersRemoved,
                $newSquadValue
            );

            return response()->json([
                'message' => 'Transfers processed successfully',
                'success' => true,
                'transfers_made' => count($playersAdded),
                'points_deducted' => max(0, count($playersAdded) - ($currentGameweek->free_transfers ?? 1)) * 4,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to process transfers: '.$e->getMessage(),
                'success' => false,
            ], 500);
        }
    }
}
