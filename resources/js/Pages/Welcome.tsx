import { Head } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/Components/ui/card";
import { PageProps } from "@/types/inertia";
import HeroSection from "../Components/ui/HeroSection";
import StatsCard from "../Components/fantasy/StatsCard";
import FantasyNavigationCard from "../Components/fantasy/FantasyNavigationCard";
import ActivityItem from "../Components/fantasy/ActivityItem";
import FixtureItem from "../Components/fantasy/FixtureItem";
import { useTenantName } from "@/hooks/useTenant";
import { useTranslation } from "react-i18next";

interface DashboardData {
    teamValue: string;
    totalPoints: number;
    globalRank: string;
    currentWeek: string;
    recentActivity: Array<{
        id: number;
        type: string;
        date: string;
        inPlayer?: {
            id: number;
            name: string;
            price: number;
        } | null;
        outPlayer?: {
            id: number;
            name: string;
            price: number;
        } | null;
    }>;
    upcomingFixtures: Array<{
        id: number;
        homeTeam: string;
        awayTeam: string;
        kickoff: string;
        gameweek: string;
    }>;
}

interface WelcomeProps extends PageProps {
    dashboardData: DashboardData;
}

export default function Welcome(props: WelcomeProps) {
    const { t, i18n } = useTranslation();
    const { auth, dashboardData } = props;
    console.log(dashboardData.recentActivity);
    const tenantName = useTenantName();
    const fantasyCards = [
        {
            title: t("My Team"),
            description: t("Manage your squad and formation"),
            icon: "👥",
            href: route("game.team"),
            color: "bg-green-500 hover:bg-green-600",
        },
        {
            title: t("Transfers"),
            description: t("Buy and sell players"),
            icon: "🔄",
            href: route("game.transfers"),
            color: "bg-blue-500 hover:bg-blue-600",
        },
        {
            title: t("Leagues"),
            description: t("Join leagues and compete"),
            icon: "🏆",
            href: route("game.leagues"),
            color: "bg-yellow-500 hover:bg-yellow-600",
        },
        {
            title: t("Fixtures"),
            description: t("View upcoming matches"),
            icon: "📅",
            href: route("game.fixtures"),
            color: "bg-purple-500 hover:bg-purple-600",
        },
    ];

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - ${t("Dashboard")}`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Hero Section */}
                {/* Hero Section */}
                <HeroSection
                    title={`${t("Welcome to")} ${tenantName}`}
                    subtitle={t(
                        "Build your dream team and compete with friends!"
                    )}
                    userWelcome={
                        auth?.user?.first_name
                            ? {
                                  firstName: auth.user.first_name,
                                  message: t("Ready to manage your team?"),
                              }
                            : undefined
                    }
                />

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <StatsCard
                        value={dashboardData.teamValue}
                        label={t("Team Value")}
                    />
                    <StatsCard
                        value={dashboardData.totalPoints.toLocaleString()}
                        label={t("Total Points")}
                    />
                    <StatsCard
                        value={dashboardData.globalRank}
                        label={t("Global League Rank")}
                    />
                    <StatsCard
                        value={dashboardData.currentWeek}
                        label={t("Current Week")}
                    />
                </div>

                {/* Fantasy Navigation Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {fantasyCards.map((card, index) => (
                        <FantasyNavigationCard
                            key={index}
                            title={card.title}
                            description={card.description}
                            icon={card.icon}
                            href={card.href}
                            color={card.color}
                        />
                    ))}
                </div>

                {/* Recent Activity & Upcoming Fixtures */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card className="transparent-card">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-gray-200">
                                <span>📊</span>
                                {t("Recent Activity")}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {dashboardData.recentActivity.length > 0 ? (
                                dashboardData.recentActivity.map((activity) => {
                                    let title = "";
                                    let description = "";
                                    let badge = "";
                                    let variant: "green" | "blue" | "amber" =
                                        "blue";

                                    if (
                                        activity.inPlayer &&
                                        activity.outPlayer
                                    ) {
                                        title = `${activity.inPlayer.name} ${t(
                                            "transferred in"
                                        )}`;
                                        description = `${
                                            activity.outPlayer.name
                                        } ${t("transferred out")}`;
                                        badge = t("IN");
                                        variant = "blue";
                                    } else if (activity.inPlayer) {
                                        title = `${activity.inPlayer.name} ${t(
                                            "transferred in"
                                        )}`;
                                        description = `${t(
                                            "Cost: £"
                                        )}${activity.inPlayer.price.toFixed(
                                            1
                                        )}${t("m")}`;
                                        badge = t("IN");
                                        variant = "blue";
                                    } else if (activity.outPlayer) {
                                        title = `${activity.outPlayer.name} ${t(
                                            "transferred out"
                                        )}`;
                                        description = `${t(
                                            "Transfer date:"
                                        )} ${new Date(
                                            activity.date
                                        ).toLocaleDateString()}`;
                                        badge = t("OUT");
                                        variant = "amber";
                                    }

                                    return (
                                        <ActivityItem
                                            key={activity.id}
                                            title={title}
                                            description={description}
                                            badge={badge}
                                            variant={variant}
                                        />
                                    );
                                })
                            ) : (
                                <div className="text-center py-4 text-gray-400">
                                    {t("No recent activity")}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card className="transparent-card">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-gray-200">
                                <span>📅</span>
                                {t("Upcoming Fixtures")}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {dashboardData.upcomingFixtures.length > 0 ? (
                                dashboardData.upcomingFixtures.map(
                                    (fixture) => (
                                        <FixtureItem
                                            key={fixture.id}
                                            homeTeam={fixture.homeTeam}
                                            awayTeam={fixture.awayTeam}
                                            kickoff={fixture.kickoff}
                                            gameweek={fixture.gameweek}
                                        />
                                    )
                                )
                            ) : (
                                <div className="text-center py-4 text-gray-400">
                                    {t("No upcoming fixtures")}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </Layout>
    );
}
