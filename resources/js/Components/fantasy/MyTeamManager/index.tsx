import React, { useState, useMemo } from "react";
import { router } from "@inertiajs/react";
import { useTranslation } from "react-i18next";

import { MyTeamManagerProps, Player } from "@/types/fantasy";
import {
    initializeTeam,
    calculateSquadValue,
    validateSwap,
    getFormationKey,
    applyFormation,
} from "@/lib/fantasy/team-management";

import StatsCard from "../StatsCard";
import MessageBox from "./MessageBox";
import PlayerInfoModal from "../common/PlayerInfoModal";
import ActionMenu from "./ActionMenu";
import ConfirmationModal from "./ConfirmationModal";
import Pitch from "../common/Pitch";
import Bench from "../common/Bench";

export default function MyTeamManager({
    teamData,
    squadPlayers,
    startingPlayerIds,
}: MyTeamManagerProps) {
    const { t } = useTranslation();

    const initialSquad = useMemo(
        () =>
            squadPlayers.map((player) => ({
                id: player.id,
                name: player.name,
                position: player.position,
                price: player.price,
                is_captain: player.is_captain,
                is_vice_captain: player.is_vice_captain,
                onPitch: false,
                pitchPosition: null,
                team_data: player.team_data,
            })),
        [squadPlayers]
    );

    const [initialPlayersState, setInitialPlayersState] = useState<Player[]>(
        () => initializeTeam(initialSquad, startingPlayerIds, t)
    );
    const [players, setPlayers] = useState<Player[]>(() =>
        JSON.parse(JSON.stringify(initialPlayersState))
    );
    const [balance, setBalance] = useState<number>(teamData.balance);
    const [selectedPlayerId, setSelectedPlayerId] = useState<number | null>(
        null
    );
    const [eligiblePlayerIds, setEligiblePlayerIds] = useState<number[] | null>(
        null
    );
    const [message, setMessage] = useState<string>(
        t("myTeam.clickPlayerForOptions")
    );
    const [modalPlayer, setModalPlayer] = useState<Player | null>(null);
    const [actionMenu, setActionMenu] = useState<{
        player: Player;
        position: { top: number; left: number };
    } | null>(null);
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);

    const squadValue = useMemo(() => calculateSquadValue(players), [players]);
    const hasChanges = useMemo(
        () => JSON.stringify(initialPlayersState) !== JSON.stringify(players),
        [initialPlayersState, players]
    );

    const getChanges = () => {
        const playersIn = players.filter(
            (p) =>
                p.onPitch &&
                !initialPlayersState.find((ip) => ip.id === p.id)?.onPitch
        );
        const playersOut = players.filter(
            (p) =>
                !p.onPitch &&
                initialPlayersState.find((ip) => ip.id === p.id)?.onPitch
        );
        const newCaptain = players.find(
            (p) =>
                p.is_captain &&
                !initialPlayersState.find((ip) => ip.id === p.id)?.is_captain
        );
        const newViceCaptain = players.find(
            (p) =>
                p.is_vice_captain &&
                !initialPlayersState.find((ip) => ip.id === p.id)
                    ?.is_vice_captain
        );
        return {
            playersIn,
            playersOut,
            captain: newCaptain,
            viceCaptain: newViceCaptain,
        };
    };

    const handlePlayerClick = (clickedPlayer: Player, element: HTMLElement) => {
        if (selectedPlayerId) {
            handleSwapSelection(clickedPlayer);
        } else {
            const rect = element.getBoundingClientRect();
            const parentRect = element
                .closest(".max-w-5xl")
                ?.getBoundingClientRect();

            const parentTop = parentRect?.top ?? 0;
            const parentLeft = parentRect?.left ?? 0;

            setActionMenu({
                player: clickedPlayer,
                position: {
                    top: rect.bottom - parentTop,
                    left: rect.left - parentLeft,
                },
            });
        }
    };

    const handleInitiateSwap = (playerToSwap: Player) => {
        setActionMenu(null);
        setMessage(
            t("myTeam.selectPlayerToSwap", { playerName: playerToSwap.name })
        );
        setSelectedPlayerId(playerToSwap.id);
        const pitchPlayers = players.filter((p) => p.onPitch);
        const potentialSwaps = players.filter(
            (p) => p.onPitch !== playerToSwap.onPitch
        );
        const eligible = potentialSwaps
            .filter((p) => {
                const playerOut = playerToSwap.onPitch ? playerToSwap : p;
                const playerIn = playerToSwap.onPitch ? p : playerToSwap;
                return validateSwap(
                    playerOut,
                    playerIn,
                    pitchPlayers,
                    balance,
                    t
                ).valid;
            })
            .map((p) => p.id);
        setEligiblePlayerIds(eligible);
    };
    const handleSwapSelection = (clickedPlayer: Player) => {
        const selectedPlayer = players.find((p) => p.id === selectedPlayerId);
        if (!selectedPlayer) return;
        if (selectedPlayer.id === clickedPlayer.id) {
            setSelectedPlayerId(null);
            setEligiblePlayerIds(null);
            setMessage(t("myTeam.selectionCancelled"));
            return;
        }
        if (!eligiblePlayerIds?.includes(clickedPlayer.id)) {
            setMessage(t("myTeam.invalidSwap"));
            setSelectedPlayerId(null);
            setEligiblePlayerIds(null);
            return;
        }
        const playerOut = selectedPlayer.onPitch
            ? selectedPlayer
            : clickedPlayer;
        const playerIn = selectedPlayer.onPitch
            ? clickedPlayer
            : selectedPlayer;
        const priceDifference = playerIn.price - playerOut.price;
        setBalance((prev) => prev - priceDifference);
        setPlayers((currentPlayers) => {
            let newPlayers = JSON.parse(JSON.stringify(currentPlayers));
            const p1 = newPlayers.find((p: Player) => p.id === playerIn.id);
            const p2 = newPlayers.find((p: Player) => p.id === playerOut.id);
            if (p1) p1.onPitch = true;
            if (p2) {
                p2.onPitch = false;
                if (p2.is_captain) {
                    p2.is_captain = false;
                    if (p1) p1.is_captain = true;
                }
                if (p2.is_vice_captain) {
                    p2.is_vice_captain = false;
                    if (p1) p1.is_vice_captain = true;
                }
            }
            const newPitchPlayers = newPlayers.filter((p: Player) => p.onPitch);
            const counts = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
            newPitchPlayers.forEach((p: Player) => {
                counts[p.position]++;
            });
            const formationKey = getFormationKey(counts);
            return applyFormation(newPlayers, formationKey, t);
        });
        setMessage(
            t("myTeam.substitutionSuccess", {
                playerIn: playerIn.name,
                playerOut: playerOut.name,
            })
        );
        setSelectedPlayerId(null);
        setEligiblePlayerIds(null);
    };
    const handleSetCaptain = (playerToMakeCaptain: Player) => {
        setPlayers((currentPlayers) => {
            const oldCaptain = currentPlayers.find((p) => p.is_captain);
            return currentPlayers.map((p) => {
                if (p.id === playerToMakeCaptain.id)
                    return { ...p, is_captain: true, is_vice_captain: false };
                if (oldCaptain && p.id === oldCaptain.id)
                    return { ...p, is_captain: false, is_vice_captain: true };
                if (
                    oldCaptain &&
                    p.id !== playerToMakeCaptain.id &&
                    p.id !== oldCaptain.id
                )
                    return { ...p, is_vice_captain: false };
                return p;
            });
        });
        setActionMenu(null);
    };
    const handleSetViceCaptain = (playerToMakeViceCaptain: Player) => {
        setPlayers((currentPlayers) => {
            const oldCaptain = currentPlayers.find((p) => p.is_captain);
            if (oldCaptain && playerToMakeViceCaptain.id === oldCaptain.id) {
                return currentPlayers.map((p) => {
                    if (p.is_captain)
                        return {
                            ...p,
                            is_captain: false,
                            is_vice_captain: true,
                        };
                    if (p.is_vice_captain)
                        return {
                            ...p,
                            is_captain: true,
                            is_vice_captain: false,
                        };
                    return p;
                });
            }
            return currentPlayers.map((p) => {
                if (p.id === playerToMakeViceCaptain.id)
                    return { ...p, is_vice_captain: true };
                if (p.is_vice_captain) return { ...p, is_vice_captain: false };
                return p;
            });
        });
        setActionMenu(null);
    };
    const handlePlayerInfoClick = (player: Player) => setModalPlayer(player);
    const handleCloseModal = () => setModalPlayer(null);

    const handleConfirmClick = () => {
        setShowConfirmationModal(true);
    };

    const handleFinalConfirm = () => {
        const pitchPlayers = players.filter((p) => p.onPitch);
        const formationCounts = pitchPlayers.reduce((acc, player) => {
            acc[player.position] = (acc[player.position] || 0) + 1;
            return acc;
        }, {} as { [key: string]: number });
        const currentFormation = getFormationKey(formationCounts);

        const lineupPlayerIds = pitchPlayers.map((p) => p.id);
        const captain = players.find((p) => p.is_captain);
        const viceCaptain = players.find((p) => p.is_vice_captain);

        if (!captain || !viceCaptain) {
            setMessage(t("myTeam.selectCaptains"));
            return;
        }

        router.post(
            route("fantasy-team.update"),
            {
                lineup_players: lineupPlayerIds,
                captain_id: captain.id,
                vice_captain_id: viceCaptain.id,
                formation: currentFormation,
            },
            {
                onSuccess: () => {
                    setInitialPlayersState(JSON.parse(JSON.stringify(players)));
                    setShowConfirmationModal(false);
                    setMessage(t("myTeam.changesSaved"));
                },
                onError: (errors) => {
                    console.error("Save failed:", errors);
                    setMessage(t("myTeam.saveFailed"));
                },
            }
        );
    };

    const handleResetClick = () => {
        setPlayers(JSON.parse(JSON.stringify(initialPlayersState)));
        setBalance(teamData.balance);
        setSelectedPlayerId(null);
        setEligiblePlayerIds(null);
        setActionMenu(null);
        setMessage(t("myTeam.changesReset"));
    };

    return (
        <div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <StatsCard
                    value={squadValue.toFixed(1)}
                    label={t("myTeam.squadValue")}
                />
                <StatsCard
                    value={balance.toFixed(1)}
                    label={t("myTeam.balance")}
                />
            </div>
            <div className="transparent-card min-h-screen p-4 sm:p-8 font-sans pb-24">
                <div className="max-w-5xl mx-auto">
                    <MessageBox
                        message={message}
                        type={
                            message.includes("Invalid") ||
                            message.includes("must") ||
                            message.includes("Not enough")
                                ? "error"
                                : "info"
                        }
                    />
                    {actionMenu && (
                        <ActionMenu
                            player={actionMenu.player}
                            position={actionMenu.position}
                            onClose={() => setActionMenu(null)}
                            onInitiateSwap={handleInitiateSwap}
                            onSetCaptain={handleSetCaptain}
                            onSetViceCaptain={handleSetViceCaptain}
                        />
                    )}
                    <Pitch
                        players={players}
                        onPlayerClick={handlePlayerClick}
                        onPlayerInfoClick={handlePlayerInfoClick}
                        selectedPlayerId={selectedPlayerId}
                        eligiblePlayerIds={eligiblePlayerIds}
                    />
                    <Bench
                        players={players}
                        onPlayerClick={handlePlayerClick}
                        onPlayerInfoClick={handlePlayerInfoClick}
                        selectedPlayerId={selectedPlayerId}
                        eligiblePlayerIds={eligiblePlayerIds}
                    />

                    <PlayerInfoModal
                        player={modalPlayer}
                        onClose={handleCloseModal}
                        context="myTeam"
                    />
                </div>
            </div>
            {showConfirmationModal && (
                <ConfirmationModal
                    changes={getChanges()}
                    onConfirm={handleFinalConfirm}
                    onCancel={() => setShowConfirmationModal(false)}
                />
            )}
            {hasChanges && !showConfirmationModal && (
                <div className="fixed bottom-0 z-10 left-0 right-0 bg-black/10 backdrop-blur-lg shadow-lg p-4 flex justify-center space-x-4">
                    <button
                        onClick={handleResetClick}
                        className="bg-red-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-red-700 transition-colors"
                    >
                        {t("myTeam.reset")}
                    </button>
                    <button
                        onClick={handleConfirmClick}
                        className="bg-green-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-green-700 transition-colors"
                    >
                        {t("myTeam.confirmChanges")}
                    </button>
                </div>
            )}
        </div>
    );
}
