<?php

namespace App\Filament\Resources\Seasons\Schemas;

use Carbon\Carbon;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class SeasonInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('competition.name')
                    ->label('Competition')
                    ->badge()
                    ->color('primary'),
                TextEntry::make('name')
                    ->label('Season Name')
                    ->weight('bold'),
                TextEntry::make('start_date')
                    ->label('Start Date')
                    ->date(),
                TextEntry::make('end_date')
                    ->label('End Date')
                    ->date(),
                TextEntry::make('duration')
                    ->label('Duration')
                    ->state(function ($record) {
                        if ($record->start_date && $record->end_date) {
                            $startDate = is_string($record->start_date)
                                ? Carbon::parse($record->start_date)
                                : $record->start_date;
                            $endDate = is_string($record->end_date)
                                ? Carbon::parse($record->end_date)
                                : $record->end_date;
                            $days = $startDate->diffInDays($endDate);

                            return $days.' days';
                        }

                        return 'N/A';
                    }),
                TextEntry::make('created_at')
                    ->label('Created')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->label('Updated')
                    ->dateTime(),
            ]);
    }
}
