<?php

namespace App\Http\Controllers;

use App\Models\FantasyTeam;
use App\Models\FantasyTransfer;
use App\Models\Gameweek;
use App\Models\RankingGlobal;
use App\Services\FantasyContextService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class HomeController extends Controller
{
    protected $fantasyContextService;

    public function __construct(FantasyContextService $fantasyContextService)
    {
        $this->fantasyContextService = $fantasyContextService;
    }

    /**
     * Display the welcome/dashboard page with dynamic data
     */
    public function index(Request $request)
    {
        $contextResult = $this->fantasyContextService->getFantasyContextWithErrors($request);

        // Default values in case of errors
        $dashboardData = [
            'teamValue' => '£0.0m',
            'totalPoints' => 0,
            'globalRank' => 'N/A',
            'currentWeek' => 'N/A',
            'recentActivity' => [],
            'upcomingFixtures' => [],
        ];

        if ($contextResult['success']) {
            $context = $contextResult['data'];
            $currentGameweek = $context['currentGameweek'];
            $competition = $context['competition'];
            $currentSeason = $context['currentSeason'];

            // Get user's fantasy team for current context
            $user = Auth::user();
            $fantasyTeam = null;

            if ($user && $currentSeason) {
                $fantasyTeam = FantasyTeam::where('user_id', $user->id)
                    ->where('season_id', $currentSeason->id)
                    ->first();
            }

            // Team value
            $squadValue = 0;
            if ($fantasyTeam) {
                $squadPlayers = $fantasyTeam->fantasyPlayers
                    ->load('player');

                $squadValue = $squadPlayers->sum(function ($fantasyPlayer) {
                    return $fantasyPlayer ? $fantasyPlayer->purchase_price : 0;
                });
            }

            // Get total points
            $totalPoints = RankingGlobal::getTotalPointsForCurrentContext();

            // Get global rank
            $globalRank = RankingGlobal::getCurrentGlobalRank();

            // Current gameweek
            $currentWeekName = $currentGameweek ? $currentGameweek->name : 'N/A';

            // Recent activity (transfers)
            $recentTransfers = FantasyTransfer::getRecentTransfers(3);

            // Upcoming fixtures (next 3 games)
            $upcomingFixtures = [];
            if ($currentGameweek) {
                $upcomingFixtures = $currentGameweek->games()
                    ->with(['homeTeam:id,name,short_name,logo', 'awayTeam:id,name,short_name,logo'])
                    ->where('status', 'scheduled')
                    ->orderBy('game_date')
                    ->limit(3)
                    ->get()
                    ->map(function ($game) use ($currentGameweek) {
                        return [
                            'id' => $game->id,
                            'homeTeam' => $game->homeTeam->name,
                            'awayTeam' => $game->awayTeam->name,
                            'kickoff' => $game->game_date,
                            'gameweek' => $currentGameweek->name,
                        ];
                    });
            }

            $dashboardData = [
                'teamValue' => '£'.number_format($squadValue, 1).'m',
                'totalPoints' => $totalPoints,
                'globalRank' => $globalRank,
                'currentWeek' => $currentWeekName,
                'recentActivity' => $recentTransfers,
                'upcomingFixtures' => $upcomingFixtures,
            ];
        }

        return Inertia::render('Welcome', [
            'dashboardData' => $dashboardData,
        ]);
    }
}
