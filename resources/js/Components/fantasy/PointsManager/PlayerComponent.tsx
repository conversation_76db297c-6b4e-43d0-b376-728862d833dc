import { Player } from "@/types/fantasy";
import React, { <PERSON> } from "react";
import PlayerShirtComponent from "../common/PlayerShirtComponent";
import { t } from "i18next";

const PlayerComponent: FC<{
    player: Player;
}> = ({ player }) => {
    return (
        <div
            className="w-20 h-20 flex flex-col items-center justify-center rounded-md transition-all duration-200 ease-in-out transform"
            style={
                player.onPitch && player.pitchPosition
                    ? {
                          position: "absolute",
                          left: `${player.pitchPosition.x}%`,
                          top: `${player.pitchPosition.y}%`,
                          transform: "translate(-50%, -50%)",
                      }
                    : {}
            }
        >
            <PlayerShirtComponent player={player} />
            <div className="flex flex-col items-center justify-center bg-black/40 w-full text-center p-1 rounded-b-md">
                <p className="text-xs text-white truncate w-full font-semibold">
                    {player.name}
                </p>
                <p className="text-sm text-white font-bold">
                    {player.points} {t("points")}
                </p>
            </div>
        </div>
    );
};

export default PlayerComponent;
