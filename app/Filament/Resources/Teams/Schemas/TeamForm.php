<?php

namespace App\Filament\Resources\Teams\Schemas;

use App\Forms\Components\ShirtCreator;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class TeamForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Team Identity')
                    ->description('Basic team information and naming')
                    ->columns(3)
                    ->columnSpan('full')
                    ->schema([
                        TextInput::make('name')
                            ->label('Team Name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Manchester United')
                            ->columnSpan(1),
                        TextInput::make('name_ar')
                            ->label('Team Name (Arabic)')
                            ->maxLength(255)
                            ->placeholder('e.g., مانشستر يونايتد')
                            ->columnSpan(1),
                        TextInput::make('short_name')
                            ->label('Short Name')
                            ->required()
                            ->maxLength(50)
                            ->placeholder('e.g., Man Utd')
                            ->columnSpan(1),
                        TextInput::make('code_name')
                            ->label('Code Name')
                            ->required()
                            ->maxLength(10)
                            ->placeholder('e.g., MUN')
                            ->helperText('3-letter team code for displays')
                            ->columnSpan(1),
                    ]),

                Section::make('Visual Identity')
                    ->description('Team logo and kit colors')
                    ->columns(2)
                    ->columnSpan('full')
                    ->schema([
                        FileUpload::make('logo')
                            ->image()
                            ->required()
                            ->disk('public')
                            ->directory('teams')
                            ->columnSpan('full'),
                        ShirtCreator::make('shirt')
                            ->label('Home Kit')
                            ->columnSpan(1),
                        ShirtCreator::make('gk_shirt')
                            ->label('Goalkeeper Kit')
                            ->columnSpan(1),
                    ]),

                Section::make('Club Information')
                    ->description('Additional team details and history')
                    ->columns(2)
                    ->columnSpan('full')
                    ->schema([
                        TextInput::make('president')
                            ->label('Club President')
                            ->maxLength(255)
                            ->placeholder('e.g., Joan Laporta')
                            ->columnSpan(1),
                        DatePicker::make('founded_at')
                            ->label('Founded Date')
                            ->helperText('When was the club founded?')
                            ->native(false)
                            ->columnSpan(1),
                        Select::make('home_stadium_id')
                            ->label('Home Stadium')
                            ->options(function () {
                                return \App\Models\Stadium::all()->pluck('name', 'id');
                            })
                            ->searchable()
                            ->columnSpan(1)
                            ->dehydrateStateUsing(fn ($state) => empty($state) ? null : $state)
                            ->helperText('Select the team\'s primary home stadium'),
                        Select::make('other_stadiums')
                            ->label('Other Stadiums')
                            ->options(function (callable $get) {
                                // Get all stadiums except the home stadium
                                $homeStadiumId = $get('home_stadium_id');

                                $query = \App\Models\Stadium::query();

                                if ($homeStadiumId) {
                                    $query->where('id', '!=', $homeStadiumId);
                                }

                                return $query->pluck('name', 'id');
                            })
                            ->multiple()
                            ->searchable()
                            ->columnSpan(1)
                            ->helperText('Select other stadiums where this team can play'),
                    ]),
            ]);
    }
}
