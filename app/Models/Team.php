<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'short_name',
        'code_name',
        'logo',
        'shirt',
        'gk_shirt',
        'president',
        'founded_at',
    ];

    protected $casts = [
        'shirt' => 'json',
        'gk_shirt' => 'json',
        'founded_at' => 'date',
    ];

    public function seasons()
    {
        return $this->belongsToMany(Season::class);
    }

    public function players()
    {
        return $this->belongsToMany(Player::class);
    }

    public function homeGames()
    {
        return $this->hasMany(Game::class, 'home_team_id');
    }

    public function awayGames()
    {
        return $this->hasMany(Game::class, 'away_team_id');
    }

    public function playerPerformances()
    {
        return $this->hasMany(PlayerPerformance::class);
    }

    public function rankingFavoriteTeams()
    {
        return $this->hasMany(RankingFavoriteTeam::class);
    }

    /**
     * The stadiums where the team plays.
     */
    public function stadiums()
    {
        return $this->belongsToMany(Stadium::class, 'stadium_team')->withPivot('is_home');
    }

    public function homeStadium()
    {
        return $this->belongsToMany(Stadium::class, 'stadium_team')
            ->wherePivot('is_home', true);
    }

    public function syncStadiums($homeStadiumId, array $otherStadiumIds = [])
    {
        // Initialize pivot data array
        $pivotData = [];

        // Only add home stadium if it's not null/empty
        if (! empty($homeStadiumId)) {
            // Remove the home stadium if it appears in others
            $otherStadiumIds = array_filter(
                $otherStadiumIds,
                fn ($value, $key) => $key != $homeStadiumId,
                ARRAY_FILTER_USE_BOTH
            );

            // Set home stadium
            $pivotData[$homeStadiumId] = ['is_home' => true];
        }

        // Add other stadiums as non-home
        foreach ($otherStadiumIds as $stadiumId => $data) {
            if (! empty($stadiumId)) {
                $pivotData[$stadiumId] = ['is_home' => false];
            }
        }

        return $this->stadiums()->sync($pivotData);
    }
}
