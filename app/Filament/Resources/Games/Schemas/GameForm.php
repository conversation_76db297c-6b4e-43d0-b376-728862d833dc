<?php

namespace App\Filament\Resources\Games\Schemas;

use App\Enums\GameStatus;
use App\Models\Referee;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class GameForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Game Setup')
                    ->description('Configure the basic game details')
                    ->columns(2)
                    ->schema([
                        Select::make('gameweek_id')
                            ->label('Gameweek')
                            ->required()
                            ->relationship('gameweek', 'name')
                            ->searchable()
                            ->preload()
                            ->columnSpan(1),
                        DateTimePicker::make('game_date')
                            ->label('Game Date & Time')
                            ->seconds(false)
                            ->native(false)
                            ->columnSpan(1),
                    ]),

                Section::make('Teams')
                    ->description('Select the competing teams')
                    ->columns(2)
                    ->schema([
                        Select::make('home_team_id')
                            ->label('Home Team')
                            ->required()
                            ->relationship(
                                name: 'homeTeam',
                                titleAttribute: 'name',
                                modifyQueryUsing: fn ($query) => $query->orderBy('name')
                            )
                            ->searchable(['name', 'short_name', 'code_name'])
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function (callable $set, $state) {
                                // Clear away team when home team changes
                                $set('away_team_id', null);

                                // Set stadium to home team's stadium
                                if ($state) {
                                    $homeTeam = \App\Models\Team::find($state);
                                    if ($homeTeam) {
                                        $homeStadium = $homeTeam->stadiums()->wherePivot('is_home', true)->first();
                                        $set('stadium_id', $homeStadium?->id);
                                    }
                                }
                            })
                            ->placeholder('Select home team')
                            ->columnSpan(1),
                        Select::make('away_team_id')
                            ->label('Away Team')
                            ->required()
                            ->relationship(
                                name: 'awayTeam',
                                titleAttribute: 'name',
                                modifyQueryUsing: fn ($query, callable $get) => $get('home_team_id')
                                        ? $query->where('id', '!=', $get('home_team_id'))->orderBy('name')
                                        : $query->orderBy('name')
                            )
                            ->searchable(['name', 'short_name', 'code_name'])
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function (callable $set, callable $get) {
                                // Update stadium options when away team changes
                                $homeTeamId = $get('home_team_id');
                                $awayTeamId = $get('away_team_id');

                                // If we have both teams, ensure stadium options are updated
                                if ($homeTeamId || $awayTeamId) {
                                    // This will trigger a refresh of the stadium options
                                    $set('stadium_id', $get('stadium_id')); // Re-set to trigger update
                                }
                            })
                            ->disabled(fn (callable $get) => ! $get('home_team_id'))
                            ->placeholder(fn (callable $get) => $get('home_team_id')
                                    ? 'Select away team'
                                    : 'Select home team first'
                            )
                            ->helperText(fn (callable $get) => $get('home_team_id')
                                    ? 'Choose the visiting team'
                                    : 'Please select the home team first'
                            )
                            ->rules([
                                'required',
                                'different:home_team_id',
                            ])
                            ->validationMessages([
                                'different' => 'The away team must be different from the home team.',
                                'required' => 'Please select an away team.',
                            ])
                            ->columnSpan(1),
                    ]),

                Section::make('Venue')
                    ->description('Select the stadium where the game will be played')
                    ->columns(1)
                    ->schema([
                        Select::make('stadium_id')
                            ->label('Stadium')
                            ->relationship('stadium', 'name')
                            ->searchable()
                            ->preload()
                            ->live()
                            ->options(function (callable $get) {
                                // Get home and away team IDs
                                $homeTeamId = $get('home_team_id');
                                $awayTeamId = $get('away_team_id');

                                if (! $homeTeamId && ! $awayTeamId) {
                                    return [];
                                }

                                // Get stadiums for both teams
                                $stadiums = [];

                                if ($homeTeamId) {
                                    $homeTeam = \App\Models\Team::find($homeTeamId);
                                    if ($homeTeam) {
                                        $homeStadiums = $homeTeam->stadiums;
                                        foreach ($homeStadiums as $stadium) {
                                            $stadiums[$stadium->id] = $stadium->name;
                                        }
                                    }
                                }

                                if ($awayTeamId) {
                                    $awayTeam = \App\Models\Team::find($awayTeamId);
                                    if ($awayTeam) {
                                        $awayStadiums = $awayTeam->stadiums;
                                        foreach ($awayStadiums as $stadium) {
                                            $stadiums[$stadium->id] = $stadium->name;
                                        }
                                    }
                                }

                                return $stadiums;
                            })
                            ->default(function (callable $get) {
                                // Default to home team's stadium
                                $homeTeamId = $get('home_team_id');
                                if ($homeTeamId) {
                                    $homeTeam = \App\Models\Team::find($homeTeamId);
                                    if ($homeTeam) {
                                        $homeStadium = $homeTeam->stadiums()->wherePivot('is_home', true)->first();

                                        return $homeStadium?->id;
                                    }
                                }

                                return null;
                            })
                            ->columnSpan(1),
                    ]),

                Section::make('Match Results')
                    ->description('Enter the final scores and game status')
                    ->columns(3)
                    ->schema([
                        TextInput::make('home_score')
                            ->label(fn (callable $get) => $get('home_team_id')
                                    ? 'Home Score'
                                    : 'Home Score'
                            )
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(99)
                            ->placeholder('0')
                            ->disabled(fn (callable $get) => $get('status') === GameStatus::SCHEDULED->value
                            )
                            ->helperText('Enter score only for completed games')
                            ->columnSpan(1),
                        TextInput::make('away_score')
                            ->label(fn (callable $get) => $get('away_team_id')
                                    ? 'Away Score'
                                    : 'Away Score'
                            )
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(99)
                            ->placeholder('0')
                            ->disabled(fn (callable $get) => $get('status') === GameStatus::SCHEDULED->value
                            )
                            ->helperText('Enter score only for completed games')
                            ->columnSpan(1),
                        Select::make('status')
                            ->label('Game Status')
                            ->required()
                            ->options(GameStatus::class)
                            ->default(GameStatus::SCHEDULED)
                            ->live()
                            ->afterStateUpdated(function (callable $set, $state) {
                                // Clear scores when status is scheduled
                                if ($state === GameStatus::SCHEDULED->value) {
                                    $set('home_score', null);
                                    $set('away_score', null);
                                }
                            })
                            ->helperText('Change status to enable score entry')
                            ->columnSpan(1),
                    ]),

                Section::make('Referee Assignment')
                    ->description('Assign referees to officiate this game')
                    ->columns(3)
                    ->schema([
                        Select::make('main_referee_id')
                            ->label('Main Referee')
                            ->options(Referee::all()->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function (callable $set, $state) {
                                // Clear assistant referees if they're the same as main referee
                                if ($state) {
                                    $set('assistant_referee_1_id', null);
                                    $set('assistant_referee_2_id', null);
                                }
                            })
                            ->placeholder('Select main referee')
                            ->columnSpan(1),
                        Select::make('assistant_referee_1_id')
                            ->label('Assistant Referee 1')
                            ->options(fn (callable $get) => Referee::when($get('main_referee_id'),
                                fn ($query, $mainRefereeId) => $query->where('id', '!=', $mainRefereeId)
                            )->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function (callable $set, $state, callable $get) {
                                // Clear assistant 2 if it's the same as assistant 1
                                if ($state && $get('assistant_referee_2_id') === $state) {
                                    $set('assistant_referee_2_id', null);
                                }
                            })
                            ->placeholder('Select assistant referee 1')
                            ->columnSpan(1),
                        Select::make('assistant_referee_2_id')
                            ->label('Assistant Referee 2')
                            ->options(fn (callable $get) => Referee::when($get('main_referee_id'),
                                fn ($query, $mainRefereeId) => $query->where('id', '!=', $mainRefereeId)
                            )->when($get('assistant_referee_1_id'),
                                fn ($query, $assistant1Id) => $query->where('id', '!=', $assistant1Id)
                            )->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->placeholder('Select assistant referee 2')
                            ->columnSpan(1),
                    ]),
            ]);
    }
}
