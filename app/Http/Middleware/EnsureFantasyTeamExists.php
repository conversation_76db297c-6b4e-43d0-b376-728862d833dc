<?php

namespace App\Http\Middleware;

use App\Services\FantasyContextService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureFantasyTeamExists
{
    public function __construct(protected FantasyContextService $fantasyContextService)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip middleware for fantasy team creation routes and competition switching to prevent redirect loop
        if (
            $request->routeIs('fantasy-team.squad') ||
            $request->routeIs('fantasy-team.squad.store') ||
            $request->routeIs('fantasy-team.create') ||
            $request->routeIs('fantasy-team.store') ||
            $request->routeIs('competition.set-current')) {
            return $next($request);
        }

        $user = Auth::user();

        // If no user, let other middleware handle this
        if (! $user) {
            return $next($request);
        }

        // The service method handles the competition/season check internally
        $fantasyTeam = $this->fantasyContextService->getUserFantasyTeam($request);

        // If no fantasy team exists, redirect to team creation page
        if (! $fantasyTeam) {
            // Before redirecting, we must ensure that a competition context actually exists.
            // Otherwise, we might redirect a user who just needs to select a competition.
            $competition = $this->fantasyContextService->getCurrentCompetition($request);
            if (! $competition || ! $competition->currentSeason) {
                return $next($request);
            }

            return redirect()->route('fantasy-team.create');
        }

        return $next($request);
    }
}
