<?php

namespace App\Services;

use App\Models\FantasyPlayer;
use App\Models\Player;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class PlayerDataService
{
    protected ?Collection $squadPlayers = null;

    protected bool $squadPlayersResolved = false;

    public function getSquadPlayers($fantasyTeamId, $gameweek): Collection
    {
        if ($this->squadPlayersResolved) {
            return $this->squadPlayers;
        }

        $this->squadPlayers = FantasyPlayer::with([
            'player.teams',
            'player.marketValues' => fn ($q) => $q->where('gameweek_id', $gameweek->id),
            'player.fantasyPoints' => fn ($q) => $q->whereHas('game', fn ($g) => $g->where('gameweek_id', $gameweek->id)),
            'fantasyTeamLineups' => fn ($q) => $q->where('gameweek_id', $gameweek->id),
        ])
            ->where('fantasy_team_id', $fantasyTeamId)
            ->where('gameweek_id', $gameweek->id)
            ->get();

        $this->squadPlayersResolved = true;

        return $this->squadPlayers;
    }

    /**
     * Normalize position names to match frontend expectations
     */
    public function normalizePosition(string $position): string
    {
        $positionMap = [
            'Goalkeeper' => 'GK',
            'GK' => 'GK',
            'Defender' => 'DEF',
            'DEF' => 'DEF',
            'CB' => 'DEF',
            'LB' => 'DEF',
            'RB' => 'DEF',
            'LWB' => 'DEF',
            'RWB' => 'DEF',
            'Midfielder' => 'MID',
            'MID' => 'MID',
            'CM' => 'MID',
            'CDM' => 'MID',
            'CAM' => 'MID',
            'LM' => 'MID',
            'RM' => 'MID',
            'Forward' => 'FWD',
            'FWD' => 'FWD',
            'ST' => 'FWD',
            'CF' => 'FWD',
            'LW' => 'FWD',
            'RW' => 'FWD',
        ];

        return $positionMap[$position] ?? 'MID';
    }

    /**
     * Transform fantasy player data for frontend consumption
     */
    public function transformFantasyPlayerData(FantasyPlayer $fantasyPlayer): array
    {
        $player = $fantasyPlayer->player;
        $marketValue = $player->marketValues->first()?->price ?? 0;
        $currentTeam = $player->currentTeam();
        $points = $player->fantasyPoints->sum('points');

        return [
            'id' => $player->id,
            'name' => $player->name,
            'position' => $this->normalizePosition($player->position->value),
            'price' => $marketValue, // Use raw database value
            'team' => $currentTeam ? $currentTeam->name : 'Unknown',
            'team_data' => $currentTeam ? $currentTeam->toArray() : null,
            'is_captain' => $fantasyPlayer->is_captain,
            'is_vice_captain' => $fantasyPlayer->is_vice_captain,
            'fantasy_player_id' => $fantasyPlayer->id,
            'points' => $points,
        ];
    }

    /**
     * Transform regular player data for frontend consumption
     */
    public function transformPlayerData(Player $player, $currentGameweek): array
    {
        $marketValue = $player->getMarketValueForGameweek($currentGameweek->id) ?? 0;

        // Get the first team (assuming current team is the first one)
        $currentTeam = $player->teams->first();

        return [
            'id' => $player->id,
            'name' => $player->name,
            'position' => $this->normalizePosition($player->position->value),
            'price' => $marketValue, // Use raw database value
            'team' => $currentTeam ? $currentTeam->name : 'Unknown',
        ];
    }

    /**
     * Get squad players data for a fantasy team and gameweek
     */
    public function getSquadPlayersData($fantasyTeamId, $currentGameweek): \Illuminate\Support\Collection
    {
        return $this->getSquadPlayers($fantasyTeamId, $currentGameweek)
            ->map(function ($fantasyPlayer) {
                return $this->transformFantasyPlayerData($fantasyPlayer);
            });
    }

    /**
     * Get available players data (excluding current squad players)
     */
    public function getAvailablePlayersData(Request $request, array $excludePlayerIds, $currentGameweek, $competition = null): LengthAwarePaginator
    {
        $query = Player::with(['teams', 'marketValues' => function ($query) use ($currentGameweek) {
            $query->where('gameweek_id', $currentGameweek->id);
        }])
            ->whereNotIn('id', $excludePlayerIds);

        // Filter by competition if provided
        if ($competition) {
            $query->whereHas('teams', function ($query) use ($competition) {
                // Teams are related to seasons, and seasons are related to competitions
                $query->whereHas('seasons', function ($q) use ($competition) {
                    $q->where('seasons.competition_id', $competition->id);
                });
            });
        }

        // Filter by Position
        if ($request->filled('position') && $request->input('position') !== 'all') {
            // This assumes your 'players' table has a 'position' column
            // that matches the values like 'FWD', 'MID', etc.
            // If not, you'll need to map them back.
            $query->where('position', $request->input('position'));
        }

        // Filter by Team
        if ($request->filled('team') && $request->input('team') !== 'all') {
            $teamName = $request->input('team');
            $query->whereHas('teams', function ($q) use ($teamName) {
                $q->where('name', $teamName);
            });
        }

        // Filter by Max Price
        if ($request->filled('maxPrice')) {
            $maxPrice = (float) $request->input('maxPrice');
            // Ensure you only check the price for the current gameweek
            $query->whereHas('marketValues', function ($q) use ($maxPrice, $currentGameweek) {
                $q->where('gameweek_id', $currentGameweek->id)->where('price', '<=', $maxPrice);
            });
        }

        $paginatedPlayers = $query->paginate(11)->withQueryString();

        $paginatedPlayers->getCollection()->transform(function ($player) use ($currentGameweek) {
            return $this->transformPlayerData($player, $currentGameweek);
        });

        return $paginatedPlayers;
    }

    /**
     * Get substitute priority based on player position
     */
    public function getSubstitutePriority(string $position): int
    {
        $normalizedPosition = $this->normalizePosition($position);

        return match ($normalizedPosition) {
            'GK' => 1,
            'DEF' => 2,
            'MID' => 3,
            'FWD' => 4,
            default => 1,
        };
    }
}
