import { Head, useForm } from '@inertiajs/react';
import Layout from '@/Components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import LeaderboardRow from '@/Components/fantasy/LeaderboardRow';
import { PageProps } from '@/types';
import { League, Ranking } from '@/types/fantasy';
import { useTranslation } from 'react-i18next';
import { Badge } from '@/Components/ui/badge';
import { Calendar, Crown, Award, Medal } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Paginated } from '@/types/pagination';
import { Button } from "@/Components/ui/button";
import LeagueNews from './LeagueNews';

interface RankingPageProps extends PageProps {
    league: League;
    standings: Paginated<Ranking>;
    seasons: { id: string; name: string }[];
    phases: { id: string; name: string }[];
    gameweeks: { id: string; name: string }[];
    selectedSeasonId: string;
    selectedPhaseId: string;
    selectedGameweekId: string;
}

export default function RankingPage({
    league,
    standings,
    seasons,
    phases,
    gameweeks,
    selectedSeasonId,
    selectedPhaseId,
    selectedGameweekId,
    ...props
}: RankingPageProps) {
    const { t } = useTranslation();
    const topThree = standings.data.slice(0, 3);
    const { get, processing } = useForm();

    const handleSelectChange = (type: 'season' | 'phase' | 'gameweek', value: string) => {
        const params: any = {
            season_id: type === 'season' ? value : selectedSeasonId,
        };

        if (type === 'phase') {
            params.phase_id = value;
        } else if (type === 'gameweek') {
            params.phase_id = selectedPhaseId;
            params.gameweek_id = value;
        }

        get(route('leagues.ranking', { league: league.id, ...params }));
    };

    return (
        <Layout {...props}>
            <Head title={`${t('Ranking for')} ${league.name}`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <Card>
                    <CardHeader>
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    {t('League Ranking')}: {league.name}
                                </CardTitle>
                                {league.invite_code && (
                                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono mt-2">
                                        {t('leaderboard.create.inviteCode')} {league.invite_code}
                                    </p>
                                )}
                            </div>
                            <div className="flex items-center gap-2 bg-primary/10 px-4 py-2 rounded-lg border border-primary/20">
                                <Calendar className="w-5 h-5 text-primary" />
                                <div>
                                    <p className="text-xs font-medium text-muted-foreground">{league.season.current_season_phase?.name}</p>
                                    <p className="font-bold text-primary">
                                        {league.season.current_gameweek?.[0]?.name || 'N/A'}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                </Card>

                {/* Top 3 Teams - Full Width */}
                {topThree.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Top 3 Teams</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {/* 2nd Place */}
                                {topThree[1] && (
                                    <div className="flex flex-col items-center">
                                        <div className="relative w-full">
                                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                                <div className="bg-slate-600 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center gap-1">
                                                    <Medal className="w-3 h-3" /> 2nd
                                                </div>
                                            </div>
                                            <div className="border-slate-200 bg-slate-50 pt-6 shadow-sm rounded-lg p-4">
                                                <div className="text-center">
                                                    <div className="w-16 h-16 rounded-full bg-slate-100 border-2 border-slate-300 flex items-center justify-center mx-auto mb-3">
                                                        <span className="text-2xl font-bold text-slate-700">2</span>
                                                    </div>
                                                    <h3 className="font-bold text-slate-800">{topThree[1].teamName}</h3>
                                                    <p className="text-sm text-slate-600">{topThree[1].name}</p>
                                                    <div className="mt-3 text-lg font-bold text-slate-900">
                                                        {topThree[1].points} pts
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* 1st Place */}
                                {topThree[0] && (
                                    <div className="flex flex-col items-center">
                                        <div className="relative w-full">
                                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                                <div className="bg-amber-600 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center gap-1">
                                                    <Crown className="w-3 h-3" /> 1st
                                                </div>
                                            </div>
                                            <div className="border-amber-100 bg-amber-50 pt-6 shadow-sm rounded-lg p-4">
                                                <div className="text-center">
                                                    <div className="w-16 h-16 rounded-full bg-amber-100 border-2 border-amber-300 flex items-center justify-center mx-auto mb-3">
                                                        <span className="text-2xl font-bold text-amber-700">1</span>
                                                    </div>
                                                    <h3 className="font-bold text-amber-800">{topThree[0].teamName}</h3>
                                                    <p className="text-sm text-amber-600">{topThree[0].name}</p>
                                                    <div className="mt-3 text-lg font-bold text-amber-900">
                                                        {topThree[0].points} pts
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* 3rd Place */}
                                {topThree[2] && (
                                    <div className="flex flex-col items-center">
                                        <div className="relative w-full">
                                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                                <div className="bg-stone-600 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center gap-1">
                                                    <Award className="w-3 h-3" /> 3rd
                                                </div>
                                            </div>
                                            <div className="border-stone-200 bg-stone-50 pt-6 shadow-sm rounded-lg p-4">
                                                <div className="text-center">
                                                    <div className="w-16 h-16 rounded-full bg-stone-100 border-2 border-stone-300 flex items-center justify-center mx-auto mb-3">
                                                        <span className="text-2xl font-bold text-stone-700">3</span>
                                                    </div>
                                                    <h3 className="font-bold text-stone-800">{topThree[2].teamName}</h3>
                                                    <p className="text-sm text-stone-600">{topThree[2].name}</p>
                                                    <div className="mt-3 text-lg font-bold text-stone-900">
                                                        {topThree[2].points} pts
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Main Content - League Standings and League News side by side */}
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
                    {/* League Standings - Takes 3/5 of the width */}
                    <div className="lg:col-span-3">
                        <Card>
                            <CardHeader className="bg-muted/50">
                                <CardTitle className="flex items-center gap-2">
                                    League Standings
                                    <Badge variant="outline" className="ml-auto">
                                        {standings.length} Teams
                                    </Badge>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                <div className="space-y-3 p-6">
                                    <div className="flex items-center gap-4 mb-6">
                                        {/* Season Dropdown */}
                                        <Select
                                            value={selectedSeasonId}
                                            onValueChange={value => handleSelectChange('season', value)}
                                            disabled={processing || seasons.length === 0}
                                        >
                                            <SelectTrigger className="w-[180px]">
                                                <SelectValue placeholder={t('Select Season')} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {seasons.map(season => (
                                                    <SelectItem key={season.id} value={season.id}>
                                                        {season.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>

                                        {/* Phase Dropdown */}
                                        <Select
                                            value={selectedPhaseId}
                                            onValueChange={value => handleSelectChange('phase', value)}
                                            disabled={processing || !selectedSeasonId || phases.length === 0}
                                        >
                                            <SelectTrigger className="w-[180px]">
                                                <SelectValue placeholder={t('Select Phase')} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {phases.map(phase => (
                                                    <SelectItem key={phase.id} value={phase.id}>
                                                        {phase.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>

                                        {/* Gameweek Dropdown */}
                                        <Select
                                            value={selectedGameweekId}
                                            onValueChange={value => handleSelectChange('gameweek', value)}
                                            disabled={processing || !selectedPhaseId || gameweeks.length === 0}
                                        >
                                            <SelectTrigger className="w-[180px]">
                                                <SelectValue placeholder={t('Select Gameweek')} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {gameweeks.map(gw => (
                                                    <SelectItem key={gw.id} value={gw.id}>
                                                        {gw.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Table Header */}
                                    <div className="grid grid-cols-12 gap-4 px-4 py-3 text-sm font-medium text-muted-foreground border-b">
                                        <div className="col-span-1">Rank</div>
                                        <div className="col-span-7">Team</div>
                                        <div className="col-span-2 text-center"></div>
                                        <div className="col-span-2 text-right">Points</div>
                                    </div>

                                    {/* Rows */}
                                    {standings.data.map((ranking, index) => (
                                        <div
                                            key={index}
                                            className={`transition-all hover:bg-muted/50 rounded-lg ${index < 3 ? "bg-muted/30" : ""}`}
                                        >
                                            <LeaderboardRow
                                                rank={ranking.rank}
                                                name={ranking.name}
                                                teamName={ranking.teamName}
                                                points={ranking.points}
                                                previousRank={ranking.previous_rank}
                                                highlight={index < 3}
                                            />
                                        </div>
                                    ))}
                                </div>

                                {/* Pagination */}
                                {(standings.prev_page_url || standings.next_page_url) && (
                                    <div className="flex justify-between pt-4 mb-4 px-6">
                                        <Button
                                            variant="outline"
                                            disabled={!standings.prev_page_url}
                                            onClick={() => standings.prev_page_url && get(standings.prev_page_url)}
                                        >
                                            Previous
                                        </Button>
                                        <Button
                                            variant="outline"
                                            disabled={!standings.next_page_url}
                                            onClick={() => standings.next_page_url && get(standings.next_page_url)}
                                        >
                                            Next
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* League News - Takes 2/5 of the width */}
                    <LeagueNews />
                </div>
            </div>
        </Layout>
    );
}