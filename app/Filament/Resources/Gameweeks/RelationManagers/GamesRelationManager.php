<?php

namespace App\Filament\Resources\Gameweeks\RelationManagers;

use App\Enums\GameStatus;
use App\Filament\Resources\Games\Schemas\GameForm;
use Filament\Actions\AssociateAction;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class GamesRelationManager extends RelationManager
{
    protected static string $relationship = 'games';

    protected static ?string $recordTitleAttribute = 'id';

    public function form(Schema $schema): Schema
    {
        return GameForm::configure($schema);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                TextColumn::make('homeTeam.name')
                    ->label('Home Team')
                    ->searchable()
                    ->weight('bold'),
                TextColumn::make('score')
                    ->label('Score')
                    ->formatStateUsing(function ($record) {
                        if ($record->home_score !== null && $record->away_score !== null) {
                            return "{$record->home_score} - {$record->away_score}";
                        }

                        return 'vs';
                    })
                    ->alignCenter()
                    ->badge()
                    ->color(fn ($record) => $record->status === GameStatus::FINISHED ? 'success' : 'gray'),
                TextColumn::make('awayTeam.name')
                    ->label('Away Team')
                    ->searchable()
                    ->weight('bold'),
                TextColumn::make('status')
                    ->badge(),
                TextColumn::make('game_date')
                    ->label('Kickoff')
                    ->dateTime('M j, H:i')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(GameStatus::class),
                Filter::make('today')
                    ->label('Today\'s Games')
                    ->query(fn ($query) => $query->whereDate('game_date', today())),
                Filter::make('finished')
                    ->label('Finished Games')
                    ->query(fn ($query) => $query->where('status', GameStatus::FINISHED)),
            ])
            ->defaultSort('game_date')
            ->headerActions([
                CreateAction::make(),
                // AssociateAction::make()
                //     ->preloadRecordSelect()
                //     ->recordSelectSearchColumns(['homeTeam.name', 'awayTeam.name'])
                //     ->recordSelectOptionsQuery(function ($query) {
                //         return $query->with(['homeTeam', 'awayTeam']);
                //     })
                //     ->recordSelect(function ($select) {
                //         return $select
                //             ->options(function () {
                //                 return \App\Models\Game::with(['homeTeam', 'awayTeam'])
                //                     ->whereDoesntHave('gameweek')
                //                     ->get()
                //                     ->mapWithKeys(function ($game) {
                //                         $homeTeam = $game->homeTeam?->name ?? 'TBD';
                //                         $awayTeam = $game->awayTeam?->name ?? 'TBD';
                //                         $date = $game->game_date ? $game->game_date->format('M j, H:i') : 'TBD';

                //                         return [$game->id => "{$homeTeam} vs {$awayTeam} - {$date}"];
                //                     })
                //                     ->toArray();
                //             })
                //             ->searchable()
                //             ->getSearchResultsUsing(function (string $search) {
                //                 return \App\Models\Game::with(['homeTeam', 'awayTeam'])
                //                     ->whereDoesntHave('gameweek')
                //                     ->where(function ($query) use ($search) {
                //                         $query->whereHas('homeTeam', function ($q) use ($search) {
                //                             $q->where('name', 'ilike', "%{$search}%");
                //                         })
                //                             ->orWhereHas('awayTeam', function ($q) use ($search) {
                //                                 $q->where('name', 'ilike', "%{$search}%");
                //                             });
                //                     })
                //                     ->limit(50)
                //                     ->get()
                //                     ->mapWithKeys(function ($game) {
                //                         $homeTeam = $game->homeTeam?->name ?? 'TBD';
                //                         $awayTeam = $game->awayTeam?->name ?? 'TBD';
                //                         $date = $game->game_date ? $game->game_date->format('M j, H:i') : 'TBD';

                //                         return [$game->id => "{$homeTeam} vs {$awayTeam} - {$date}"];
                //                     })
                //                     ->toArray();
                //             });
                //     }),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ]);

    }
}
