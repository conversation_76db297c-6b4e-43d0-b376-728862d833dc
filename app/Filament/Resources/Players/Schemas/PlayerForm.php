<?php

namespace App\Filament\Resources\Players\Schemas;

use App\Data\Countries;
use App\Enums\PlayerPosition;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class PlayerForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Personal Information')
                    ->description('Basic player details and background')
                    ->columns(2)
                    ->schema([
                        TextInput::make('name')
                            ->label('Player Name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., <PERSON>')
                            ->columnSpan(1),
                        TextInput::make('name_ar')
                            ->label('Player Name (Arabic)')
                            ->maxLength(255)
                            ->placeholder('e.g., محمد صلاح')
                            ->columnSpan(1),
                        DatePicker::make('birthday')
                            ->label('Date of Birth')
                            ->maxDate(now()->subYears(16))
                            ->minDate(now()->subYears(45))
                            ->columnSpan(1),
                        Select::make('country')
                            ->options(Countries::all())
                            ->searchable()
                            ->placeholder('Select country')
                            ->columnSpan(1),
                        Select::make('position')
                            ->options(PlayerPosition::class)
                            ->required()
                            ->default(PlayerPosition::MIDFIELDER)
                            ->columnSpan(2),
                    ]),

                Section::make('Media')
                    ->description('Player photo and visual assets')
                    ->columns(1)
                    ->schema([
                        FileUpload::make('image')
                            ->label('Player Photo')
                            ->image()
                            ->directory('players')
                            ->disk('public')
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '1:1',
                            ])
                            ->columnSpan('full'),
                    ]),
            ]);
    }
}
