import { Paginated } from "./pagination";

export interface TeamData {
    budget: number;
    squadValue: number;
    balance: number;
    freeTransfers: number;
}

export interface SquadPlayer {
    id: number;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    price: number;
    team: string;
    is_captain: boolean;
    is_vice_captain: boolean;
    fantasy_player_id: number;
    team_data: {
        logo: string;
        shirt: {
            base_color: string;
            pattern_type: string;
            pattern_color: string;
        };
    };
    points: number;
}

export interface League {
    id: number;
    name: string;
    season_id: number;
    owner_id: number;
    type: string;
    invite_code: string | null;
    created_at: string;
    updated_at: string;
    tenant_id: number;
    season: object;
    fantasy_teams: FantasyTeam[];
}

export interface Ranking {
    rank: number;
    name: string;
    teamName: string;
    points: number;
    lastWeek: number;
}

export interface FantasyTeam {
    id: number;
    name: string;
    kit_type: string;
    kit_primary_color: string;
    kit_secondary_color: string;
}

export interface Gameweek {
    id: number;
    name: string;
    deadline: string;
}

export interface CurrentGameweek {
    id: number;
    name: string;
    deadline: string;
}

export interface MyTeamManagerProps {
    teamData: TeamData;
    squadPlayers: SquadPlayer[];
    startingPlayerIds: number[];
    fantasyTeam: FantasyTeam;
    currentGameweek: CurrentGameweek;
}

export interface PointsManagerProps {
    squadPlayers: SquadPlayer[];
    startingPlayerIds: number[];
    fantasyTeam: FantasyTeam;
}

export interface BasePlayer {
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
}

export interface TeamData {
    budget: number;
    squadValue: number;
    balance: number;
    freeTransfers: number;
}

export interface Player {
    id: number | string;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    team: string;
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
}

export interface SquadPlayer {
    id: number;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    price: number;
    team: string;
    is_captain: boolean;
    is_vice_captain: boolean;
    fantasy_player_id: number;
}

export interface FantasyTeam {
    id: number;
    name: string;
    kit_type: string;
    kit_primary_color: string;
    kit_secondary_color: string;
}

export interface CurrentGameweek {
    id: number;
    name: string;
    deadline: string;
}

export interface Player extends BasePlayer {
    id: number;
    onPitch: boolean;
    pitchPosition: { x: number; y: number } | null;
    team_data: {
        logo: string;
        shirt: {
            base_color: string;
            pattern_type: string;
            pattern_color: string;
        };
    };
    points: number;
}

export interface PositionLayout {
    x: number;
    y: number;
}

export interface FormationLayout {
    GK: PositionLayout[];
    DEF: PositionLayout[];
    MID: PositionLayout[];
    FWD: PositionLayout[];
}

export interface Formations {
    [key: string]: FormationLayout;
}

export interface ConfirmationModalProps {
    changes: {
        playersIn: Player[];
        playersOut: Player[];
        captain: Player | undefined;
        viceCaptain: Player | undefined;
    };
    onConfirm: () => void;
    onCancel: () => void;
}

export interface TransferPlayer extends BasePlayer {
    id: number | string; // Can be number for real players, string for placeholders
    team: string;
}

export interface TransferManagerProps {
    isCreationMode?: boolean;
    initialSquadData?: TransferPlayer[];
    availablePlayers: Paginated<TransferPlayer>;
    initialBudget?: number;
    onSquadSubmit?: (squadData: {
        players: TransferPlayer[];
        captain: TransferPlayer | null;
        viceCaptain: TransferPlayer | null;
        budget: number;
    }) => void;
}

export const formations: Formations = {
    "4-4-2": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 20, y: 35 },
            { x: 40, y: 35 },
            { x: 60, y: 35 },
            { x: 80, y: 35 },
        ],
        MID: [
            { x: 20, y: 60 },
            { x: 40, y: 60 },
            { x: 60, y: 60 },
            { x: 80, y: 60 },
        ],
        FWD: [
            { x: 40, y: 85 },
            { x: 60, y: 85 },
        ],
    },
    "4-3-3": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 20, y: 35 },
            { x: 40, y: 35 },
            { x: 60, y: 35 },
            { x: 80, y: 35 },
        ],
        MID: [
            { x: 30, y: 60 },
            { x: 50, y: 60 },
            { x: 70, y: 60 },
        ],
        FWD: [
            { x: 25, y: 85 },
            { x: 50, y: 85 },
            { x: 75, y: 85 },
        ],
    },
    "3-5-2": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 30, y: 35 },
            { x: 50, y: 35 },
            { x: 70, y: 35 },
        ],
        MID: [
            { x: 15, y: 60 },
            { x: 35, y: 60 },
            { x: 50, y: 60 },
            { x: 65, y: 60 },
            { x: 85, y: 60 },
        ],
        FWD: [
            { x: 40, y: 85 },
            { x: 60, y: 85 },
        ],
    },
    "5-3-2": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 15, y: 35 },
            { x: 30, y: 35 },
            { x: 50, y: 35 },
            { x: 70, y: 35 },
            { x: 85, y: 35 },
        ],
        MID: [
            { x: 30, y: 60 },
            { x: 50, y: 60 },
            { x: 70, y: 60 },
        ],
        FWD: [
            { x: 40, y: 85 },
            { x: 60, y: 85 },
        ],
    },
    "4-5-1": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 20, y: 35 },
            { x: 40, y: 35 },
            { x: 60, y: 35 },
            { x: 80, y: 35 },
        ],
        MID: [
            { x: 15, y: 60 },
            { x: 35, y: 60 },
            { x: 50, y: 60 },
            { x: 65, y: 60 },
            { x: 85, y: 60 },
        ],
        FWD: [{ x: 50, y: 85 }],
    },
    "3-4-3": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 30, y: 35 },
            { x: 50, y: 35 },
            { x: 70, y: 35 },
        ],
        MID: [
            { x: 20, y: 60 },
            { x: 40, y: 60 },
            { x: 60, y: 60 },
            { x: 80, y: 60 },
        ],
        FWD: [
            { x: 25, y: 85 },
            { x: 50, y: 85 },
            { x: 75, y: 85 },
        ],
    },
    "5-4-1": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 15, y: 35 },
            { x: 30, y: 35 },
            { x: 50, y: 35 },
            { x: 70, y: 35 },
            { x: 85, y: 35 },
        ],
        MID: [
            { x: 20, y: 60 },
            { x: 40, y: 60 },
            { x: 60, y: 60 },
            { x: 80, y: 60 },
        ],
        FWD: [{ x: 50, y: 85 }],
    },
};
