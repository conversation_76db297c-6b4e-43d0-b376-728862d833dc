import { Head, router } from "@inertiajs/react";
import Layout from "../../Components/Layout";
import { PageProps } from "../../types/inertia";
import { useTenantName } from "../../hooks/useTenant";
import HeroSection from "@/Components/ui/HeroSection";
import PointsManager from "@/Components/fantasy/PointsManager";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/Components/ui/dropdown-menu";
import { Button } from "@/Components/ui/button";
import StatsCard from "@/Components/fantasy/StatsCard";

import { SquadPlayer, FantasyTeam, Gameweek } from "@/types/fantasy";
import { t } from "i18next";

interface PointsPageProps extends PageProps {
    squadPlayers: SquadPlayer[];
    startingPlayerIds: number[];
    fantasyTeam: FantasyTeam;
    gameweeks: Gameweek[];
    selectedGameweek: Gameweek;
}

export default function Points(props: PointsPageProps) {
    const {
        squadPlayers,
        startingPlayerIds,
        fantasyTeam,
        gameweeks,
        selectedGameweek,
    } = props;
    const tenantName = useTenantName();

    const handleGameweekChange = (gameweekId: number) => {
        router.get(route("game.points", { gameweek_id: gameweekId }));
    };

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - Points`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                <HeroSection
                    title={t("points")}
                    subtitle={`View your team's performance for ${selectedGameweek.name}`}
                />

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                    <StatsCard
                        value="120"
                        label="Your Points"
                        className="col-span-full lg:col-span-1"
                    />
                    <StatsCard value="3" label="Transfers Used" />
                    <StatsCard value="#123" label="GW Rank" />
                    <StatsCard value="150" label="Highest Points" />
                    <StatsCard value="90" label="Average Points" />
                </div>

                <div className="my-4">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline">
                                {selectedGameweek.name}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            {gameweeks.map((gw) => (
                                <DropdownMenuItem
                                    key={gw.id}
                                    onSelect={() => handleGameweekChange(gw.id)}
                                >
                                    {gw.name}
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                <PointsManager
                    squadPlayers={squadPlayers}
                    startingPlayerIds={startingPlayerIds}
                    fantasyTeam={fantasyTeam}
                />
            </div>
        </Layout>
    );
}
