<?php

namespace App\Filament\Resources\Gameweeks\Schemas;

use App\Enums\GameweekStatus;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class GameweekForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->description('Configure the gameweek details and association')
                    ->columns(2)
                    ->schema([
                        Select::make('season_phase_id')
                            ->label('Season Phase')
                            ->required()
                            ->relationship('seasonPhase', 'name')
                            ->searchable()
                            ->preload()
                            ->columnSpan(1),
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Gameweek 1')
                            ->columnSpan(1),
                    ]),

                Section::make('Schedule')
                    ->description('Set the gameweek start and end dates')
                    ->columns(2)
                    ->schema([
                        DateTimePicker::make('start_date')
                            ->required()
                            ->label('Start Date & Time')
                            ->columnSpan(1),
                        DateTimePicker::make('end_date')
                            ->required()
                            ->label('End Date & Time')
                            ->after('start_date')
                            ->columnSpan(1),
                    ]),

                Section::make('Configuration')
                    ->description('Define gameweek rules and status')
                    ->columns(1)
                    ->schema([
                        Textarea::make('rules')
                            ->label('Rules (JSON)')
                            ->required()
                            ->placeholder('{"transfers_allowed": 2, "captain_multiplier": 2, "vice_captain_multiplier": 1.5, "bench_boost": false, "triple_captain": false, "wildcard": false}')
                            ->default('{"transfers_allowed": 2, "captain_multiplier": 2, "vice_captain_multiplier": 1.5, "bench_boost": false, "triple_captain": false, "wildcard": false}')
                            ->rows(4)
                            ->helperText('Enter gameweek rules in JSON format. This field is required.')
                            ->rules(['required', 'json'])
                            ->validationMessages([
                                'json' => 'The rules field must be valid JSON.',
                            ])
                            ->columnSpan('full'),
                        Select::make('status')
                            ->required()
                            ->options(GameweekStatus::class)
                            ->default(GameweekStatus::UPCOMING)
                            ->columnSpan('full'),
                    ]),
            ]);
    }
}
