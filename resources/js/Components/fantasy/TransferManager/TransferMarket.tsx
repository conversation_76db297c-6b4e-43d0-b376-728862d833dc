import React, { FC, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";
import MarketPlayer from "./MarketPlayer";
import { validateTransfer } from "@/lib/fantasy/transfer-management";
import { Paginated } from "@/types/pagination";

interface TransferFilters {
    position: string;
    team: string;
    sort: string;
    maxPrice: number | null;
    page?: number;
}

const TransferMarket: FC<{
    paginatedPlayers: Paginated<TransferPlayer>;
    onPlayerClick: (player: TransferPlayer) => void;
    onInfoClick: (player: TransferPlayer) => void;
    selectedPlayerId: number | string | null;
    squad: TransferPlayer[];
    balance: number;
    filters: TransferFilters;
    setFilters: React.Dispatch<React.SetStateAction<TransferFilters>>;
}> = ({
    paginatedPlayers,
    onPlayerClick,
    onInfoClick,
    selectedPlayerId,
    squad,
    balance,
    filters,
    setFilters,
}) => {
    const { t } = useTranslation();
    const { data: players = [], meta } = paginatedPlayers || {
        data: [],
        meta: {},
    };

    const squadIds = useMemo(() => new Set(squad.map((p) => p.id)), [squad]);

    const teams = useMemo(
        () => [...new Set(players.map((p) => p.team))].sort(),
        [players]
    );

    const [isLoading, setIsLoading] = React.useState(false);

    const handlePageChange = (url: string | null) => {
        if (url) {
            setIsLoading(true);
            const page = new URL(url).searchParams.get("page");
            setFilters({ ...filters, page: page ? parseInt(page, 10) : 1 });
        }
    };

    useEffect(() => {
        setIsLoading(false);
    }, [paginatedPlayers]);

    return (
        <div className="transparent-card p-4 rounded-lg w-full lg:w-1/2">
            <h2 className="text-white text-xl font-bold mb-4 text-center">
                {t("transferManager.titles.transferMarket")}
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <select
                    value={filters.position}
                    onChange={(e) =>
                        setFilters({
                            ...filters,
                            position: e.target.value,
                            page: 1,
                        })
                    }
                    className="bg-gray-700 text-white p-2 rounded-md"
                >
                    <option value="all">
                        {t("transferManager.filters.allPositions")}
                    </option>
                    <option value="GK">GK</option>
                    <option value="DEF">DEF</option>
                    <option value="MID">MID</option>
                    <option value="FWD">FWD</option>
                </select>
                <select
                    value={filters.team}
                    onChange={(e) =>
                        setFilters({
                            ...filters,
                            team: e.target.value,
                            page: 1,
                        })
                    }
                    className="bg-gray-700 text-white p-2 rounded-md"
                >
                    <option value="all">
                        {t("transferManager.filters.allTeams")}
                    </option>
                    {teams.map((team) => (
                        <option key={team} value={team}>
                            {team}
                        </option>
                    ))}
                </select>
                <input
                    type="number"
                    placeholder={t("transferManager.filters.maxPrice")}
                    value={filters.maxPrice ?? ""}
                    onChange={(e) =>
                        setFilters({
                            ...filters,
                            maxPrice: e.target.value
                                ? parseFloat(e.target.value)
                                : null,
                            page: 1,
                        })
                    }
                    className="bg-gray-700 text-white p-2 rounded-md"
                />
                <button
                    onClick={() =>
                        setFilters({
                            position: "all",
                            team: "all",
                            sort: "price_desc",
                            maxPrice: null,
                            page: 1,
                        })
                    }
                    className="bg-red-500 text-white p-2 rounded-md"
                >
                    {t("transferManager.actions.resetFilters")}
                </button>
            </div>
            <div className="overflow-y-auto h-fit">
                <table className="w-full text-left text-white">
                    <thead className="sticky top-0 bg-gray-900">
                        <tr>
                            <th className="p-2">
                                {t("transferManager.market.player")}
                            </th>
                            <th className="p-2">
                                {t("transferManager.market.team")}
                            </th>
                            <th className="p-2">
                                {t("transferManager.market.pos")}
                            </th>
                            <th className="p-2 text-right">
                                {t("transferManager.market.price")}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {players.map((p) => {
                            const isInSquad = squadIds.has(p.id);
                            const selectedSquadPlayer = squad.find(
                                (sp) => sp.id === selectedPlayerId
                            );
                            const isEligible = selectedSquadPlayer
                                ? validateTransfer(
                                      selectedSquadPlayer,
                                      p,
                                      squad,
                                      balance,
                                      t
                                  ).valid
                                : false;
                            return (
                                <MarketPlayer
                                    key={p.id}
                                    player={p}
                                    onPlayerClick={onPlayerClick}
                                    onInfoClick={onInfoClick}
                                    isEligible={isEligible}
                                    isInSquad={isInSquad}
                                />
                            );
                        })}
                    </tbody>
                </table>
            </div>
            <div className="flex justify-between items-center mt-4">
                <button
                    onClick={() =>
                        handlePageChange(paginatedPlayers.prev_page_url)
                    }
                    disabled={!paginatedPlayers.prev_page_url}
                    className={`bg-gray-600 cursor-pointer text-white font-bold py-2 px-4 rounded disabled:cursor-not-allowed disabled:opacity-50 ${
                        isLoading && "opacity-50"
                    }`}
                >
                    {t("pagination.previous")}
                </button>
                <span className="text-white">
                    {t("pagination.page", {
                        current: meta?.current_page ?? 1,
                        total: meta?.last_page ?? 1,
                    })}
                </span>
                <button
                    onClick={() =>
                        handlePageChange(paginatedPlayers.next_page_url)
                    }
                    disabled={!paginatedPlayers.next_page_url}
                    className={`bg-gray-600 cursor-pointer text-white font-bold py-2 px-4 rounded disabled:cursor-not-allowed disabled:opacity-50 ${
                        isLoading && "opacity-50"
                    }`}
                >
                    {t("pagination.next")}
                </button>
            </div>
        </div>
    );
};

export default TransferMarket;
