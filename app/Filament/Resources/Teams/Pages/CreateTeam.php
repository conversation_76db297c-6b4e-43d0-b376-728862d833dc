<?php

namespace App\Filament\Resources\Teams\Pages;

use App\Filament\Resources\Teams\TeamResource;
use Filament\Resources\Pages\CreateRecord;

class CreateTeam extends CreateRecord
{
    protected static string $resource = TeamResource::class;

    protected ?int $homeStadiumId = null;

    protected array $otherStadiums = [];

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Store stadium data separately to handle after create
        $this->homeStadiumId = $data['home_stadium_id'] ?? null;
        $this->otherStadiums = $data['other_stadiums'] ?? [];

        return $data;
    }

    protected function afterCreate(): void
    {
        // Prepare other stadiums array only, no home stadium here
        $otherStadiums = [];

        foreach ($this->otherStadiums as $stadiumId) {
            $otherStadiums[$stadiumId] = ['is_home' => false];
        }

        // Get the single home stadium id
        $homeStadiumId = $this->homeStadiumId;
        // Call syncStadiums with proper parameters
        $this->record->syncStadiums($homeStadiumId, $otherStadiums);
    }
}
