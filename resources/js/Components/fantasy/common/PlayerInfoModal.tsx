import React, { FC } from "react";
import { useTranslation } from "react-i18next";
import { Player, TransferPlayer } from "@/types/fantasy";

const PlayerInfoModal: FC<{
    player: Player | TransferPlayer | null;
    onClose: () => void;
    context: "myTeam" | "transferManager";
}> = ({ player, onClose, context }) => {
    const { t } = useTranslation();
    if (!player) return null;

    const isMyTeamPlayer = (p: any): p is Player => context === "myTeam";

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div
                className={`bg-white rounded-lg p-6 w-11/12 max-w-md ${
                    isMyTeamPlayer(player) ? "text-gray-800" : ""
                }`}
            >
                <h2
                    className={`font-bold ${
                        isMyTeamPlayer(player)
                            ? "text-lg"
                            : "text-2xl mb-4"
                    }`}
                >
                    {isMyTeamPlayer(player)
                        ? t("myTeam.playerActions")
                        : player.name}
                </h2>
                <p>
                    <span className="font-bold">
                        {t(`${context}.playerInfo.position`)}
                    </span>
                    {": "}
                    {player.position}
                </p>
                <p>
                    <span className="font-bold">
                        {t(`${context}.playerInfo.price`)}
                    </span>
                    {": "}
                    {player.price.toFixed(1)}
                </p>
                {isMyTeamPlayer(player) && "onPitch" in player && (
                    <p>
                        <span className="font-bold">
                            {t("myTeam.playerInfo.status")}
                        </span>
                        {": "}
                        {player.onPitch
                            ? t("myTeam.onPitch")
                            : t("myTeam.onBench")}
                    </p>
                )}
                {!isMyTeamPlayer(player) && "team" in player && (
                    <p>
                        <span className="font-bold">
                            {t("transferManager.playerInfo.team")}
                        </span>
                        {": "}
                        {player.team}
                    </p>
                )}
                <p className="mt-4 text-gray-600">
                    {t(`${context}.playerInfo.statsPlaceholder`)}
                </p>
                <button
                    onClick={onClose}
                    className="mt-6 bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-700 w-full"
                >
                    {t(`${context}.actions.close`)}
                </button>
            </div>
        </div>
    );
};

export default PlayerInfoModal;