<?php

namespace App\Filament\Resources\Teams\Pages;

use App\Filament\Resources\Teams\TeamResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditTeam extends EditRecord
{
    protected static string $resource = TeamResource::class;

    protected ?int $homeStadiumId = null;

    protected array $otherStadiums = [];

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Store stadium data separately to handle after save
        $this->homeStadiumId = $data['home_stadium_id'] ?? null;
        $this->otherStadiums = $data['other_stadiums'] ?? [];

        return $data;
    }

    protected function afterSave(): void
    {
        // Handle stadium associations after saving the team
        $stadiumIds = [];

        // Add home stadium if selected
        if ($this->homeStadiumId) {
            $stadiumIds[$this->homeStadiumId] = ['is_home' => true];
        }

        // Add other stadiums

        foreach ($this->otherStadiums as $stadiumId) {
            if (! isset($stadiumIds[$stadiumId])) {
                $stadiumIds[$stadiumId] = ['is_home' => false];
            }
        }
        $homeStadiumId = $this->homeStadiumId;

        // Sync stadiums with the team using the model's method
        $this->record->syncStadiums($homeStadiumId, $stadiumIds);
    }
}
