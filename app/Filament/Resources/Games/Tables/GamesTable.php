<?php

namespace App\Filament\Resources\Games\Tables;

use App\Enums\GameStatus;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class GamesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('gameweek.name')
                    ->label('Gameweek')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('homeTeam.name')
                    ->label('Home Team')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('home_score')
                    ->label('Score')
                    ->formatStateUsing(fn ($record) => $record->home_score !== null && $record->away_score !== null
                            ? "{$record->home_score} - {$record->away_score}"
                            : '-'
                    )
                    ->alignCenter(),
                TextColumn::make('awayTeam.name')
                    ->label('Away Team')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('stadium.name')
                    ->label('Stadium')
                    ->sortable()
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('game_date')
                    ->label('Date & Time')
                    ->dateTime('M j, Y H:i')
                    ->sortable(),
                TextColumn::make('status')
                    ->badge()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(GameStatus::class),
                SelectFilter::make('gameweek')
                    ->relationship('gameweek', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
