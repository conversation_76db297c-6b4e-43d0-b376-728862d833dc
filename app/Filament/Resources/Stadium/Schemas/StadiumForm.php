<?php

namespace App\Filament\Resources\Stadium\Schemas;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class StadiumForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Stadium Identity')
                    ->description('Basic stadium information and location')
                    ->columns(3)
                    ->columnSpan('full')
                    ->schema([
                        TextInput::make('name')
                            ->label('Stadium Name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Old Trafford')
                            ->columnSpan(2),
                        TextInput::make('location')
                            ->label('Location')
                            ->maxLength(255)
                            ->placeholder('e.g., Greater Manchester')
                            ->columnSpan(1),
                        TextInput::make('city')
                            ->label('City')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Manchester')
                            ->columnSpan(1),
                        TextInput::make('country')
                            ->label('Country')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., England')
                            ->columnSpan(1),
                    ]),

                Section::make('Stadium Details')
                    ->description('Physical characteristics and history')
                    ->columns(2)
                    ->columnSpan('full')
                    ->schema([
                        TextInput::make('capacity')
                            ->label('Seating Capacity')
                            ->numeric()
                            ->minValue(0)
                            ->placeholder('e.g., 74,000')
                            ->columnSpan(1),
                        TextInput::make('year_opened')
                            ->label('Year Opened')
                            ->numeric()
                            ->minValue(1800)
                            ->maxValue(date('Y'))
                            ->placeholder('e.g., 1910')
                            ->columnSpan(1),
                    ]),

                Section::make('Visual Identity')
                    ->description('Stadium image')
                    ->columns(1)
                    ->columnSpan('full')
                    ->schema([
                        FileUpload::make('image_url')
                            ->label('Stadium Image')
                            ->image()
                            ->disk('public')
                            ->directory('stadiums')
                            ->columnSpan('full'),
                    ]),
            ]);
    }
}
