import React, { useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/Components/ui/card";
import { But<PERSON> } from "@/Components/ui/button";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/Components/ui/select";
import * as Tabs from "@radix-ui/react-tabs";
import { PageProps } from "../types/inertia";
import HeroSection from "../Components/ui/HeroSection";
import LeagueCard from "../Components/fantasy/LeagueCard";
import { useTenantName } from "@/hooks/useTenant";
import { useTranslation } from "react-i18next";
import { Camera, Upload } from 'lucide-react';

export default function Leagues({
    myLeagues,
    availableLeagues,
    ...props
}: PageProps & { myLeagues: any[]; availableLeagues: any[] }) {
    const tenantName = useTenantName();
    const { t } = useTranslation();
    const [activeTab, setActiveTab] = useState("my-leagues");

    // Form for creating new league
    const {
        data: createData,
        setData: setCreateData,
        post: createPost,
        processing: createProcessing,
        errors: createErrors,
        reset: resetCreateForm,
        setErrors: setCreateErrors,
    } = useForm({
        name: "",
        type: "public",
        logo: null as File | null,
        logoPreview: "",
    });

    // Form for joining by invite code
    const {
        data: joinData,
        setData: setJoinData,
        post: joinPost,
        processing: joinProcessing,
        errors: joinErrors,
    } = useForm({
        invite_code: "",
    });

    const handleCreateLeague = (e: React.FormEvent) => {
        e.preventDefault();

        // Create FormData for file upload
        const formData = new FormData();
        formData.append('name', createData.name);
        formData.append('type', createData.type);

        if (createData.logo) {
            formData.append('logo', createData.logo);
        }

        // Use Inertia's post method with form data
        createPost(route('leagues.store'), {
            _method: 'post',
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            onSuccess: () => {
                // Reset form on success
                setCreateData({
                    name: '',
                    type: 'private',
                    logo: null,
                    logoPreview: '',
                });
            },
            preserveScroll: true,
        });
    };

    const handleJoinByCode = (e: React.FormEvent) => {
        e.preventDefault();
        joinPost(route("leagues.join-by-code"));
    };

    const handleJoinLeague = (leagueId: number) => {
        createPost(route("leagues.join", leagueId));
    };

    const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            // Validate file type and size
            const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
            const maxSize = 2 * 1024 * 1024; // 2MB

            if (!validTypes.includes(file.type)) {
                setCreateErrors({
                    ...createErrors,
                    logo: 'Invalid file type. Please upload a JPEG, PNG, or GIF image.',
                });
                return;
            }

            if (file.size > maxSize) {
                setCreateErrors({
                    ...createErrors,
                    logo: 'File is too large. Maximum size is 2MB.',
                });
                return;
            }

            // Create preview URL
            const previewUrl = URL.createObjectURL(file);

            setCreateData({
                ...createData,
                logo: file,
                logoPreview: previewUrl,
            });

            // Clear any previous errors
            if (createErrors.logo) {
                setCreateErrors({
                    ...createErrors,
                    logo: '',
                });
            }
        }
    };
    console.log("availableLeagues", availableLeagues);

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - ${t("leaderboard.title")} `} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title={t("leaderboard.heroTitle")}
                    subtitle={t("leaderboard.heroSubtitle")}
                />

                {/* Tabs */}
                <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
                    <div className="flex justify-center">
                        <Tabs.List className="grid w-full grid-cols-3 bg-slate-100 rounded-lg p-1 max-w-md">
                            <Tabs.Trigger
                                value="my-leagues"
                                className={`px-4 py-2 text-sm font-medium rounded-md ${activeTab === "my-leagues"
                                        ? "bg-background text-foreground shadow-sm"
                                        : "text-gray-500"
                                    }`}
                            >
                                {t("leaderboard.tabs.myLeagues")}
                            </Tabs.Trigger>
                            <Tabs.Trigger
                                value="join-league"
                                className={`px-4 py-2 text-sm font-medium rounded-md ${activeTab === "join-league"
                                        ? "bg-background text-foreground shadow-sm"
                                        : "text-gray-500"
                                    }`}
                            >
                                {t("leaderboard.tabs.joinLeague")}
                            </Tabs.Trigger>
                            <Tabs.Trigger
                                value="create-league"
                                className={`px-4 py-2 text-sm font-medium rounded-md ${activeTab === "create-league"
                                        ? "bg-background text-foreground shadow-sm"
                                        : "text-gray-500"
                                    }`}
                            >
                                {t("leaderboard.tabs.createLeague")}
                            </Tabs.Trigger>
                        </Tabs.List>
                    </div>

                    {/* My Leagues Content */}
                    <Tabs.Content value="my-leagues" className="mt-8 space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {myLeagues.map((league) => (
                                <LeagueCard
                                    key={league.id}
                                    {...league}
                                    type={league.type as "Private" | "Public"}
                                    isJoined={true}
                                    logo={league.logo}
                                />
                            ))}
                        </div>
                    </Tabs.Content>

                    {/* Join League Content */}
                    <Tabs.Content value="join-league" className="mt-8 space-y-6">
                        <Card className="max-w-md mx-auto">
                            <CardHeader>
                                <CardTitle>
                                    {t("leaderboard.join.privateTitle")}
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <form
                                    onSubmit={handleJoinByCode}
                                    className="space-y-4"
                                >
                                    <div>
                                        <Label htmlFor="invite_code">
                                            {t("leaderboard.join.privateTitle")}
                                        </Label>
                                        <Input
                                            id="invite_code"
                                            type="text"
                                            value={joinData.invite_code}
                                            onChange={(e) =>
                                                setJoinData(
                                                    "invite_code",
                                                    e.target.value
                                                )
                                            }
                                            placeholder={t(
                                                "leaderboard.join.placeholder"
                                            )}
                                            className="mt-1"
                                        />
                                        {joinErrors.invite_code && (
                                            <p className="text-red-500 text-xs mt-1">
                                                {joinErrors.invite_code}
                                            </p>
                                        )}
                                    </div>
                                    <Button
                                        type="submit"
                                        className="w-full"
                                        disabled={joinProcessing}
                                    >
                                        {joinProcessing
                                            ? t("leaderboard.join.joining")
                                            : t("leaderboard.join.joinButton")}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {
                            availableLeagues.map((league) => (
                                <LeagueCard
                                    key={league.id}
                                    {...league}
                                    type={league.type as "Private" | "Public"}
                                    isJoined={false}
                                    onJoin={handleJoinLeague}
                                    logo={league.logo}
                                />
                            ))}
                        </div>
                    </Tabs.Content>

                    {/* Create League Content - Enhanced Section */}
                    <Tabs.Content value="create-league" className="mt-8">
                        <Card className="max-w-2xl mx-auto">
                            <CardHeader>
                                <CardTitle className="text-2xl font-bold">
                                    {t("leaderboard.create.title")}
                                </CardTitle>
                                <CardDescription>
                                    {t("leaderboard.create.subtitle")}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleCreateLeague} className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                        {/* Left Column - Form Fields */}
                                        <div className="space-y-6">
                                            {/* League Name */}
                                            <div className="space-y-2">
                                                <Label htmlFor="name" className="text-sm font-medium">
                                                    {t("leaderboard.create.leagueName")}
                                                    <span className="text-red-500 ml-1">*</span>
                                                </Label>
                                                <Input
                                                    id="name"
                                                    type="text"
                                                    value={createData.name}
                                                    onChange={(e) =>
                                                        setCreateData("name", e.target.value)
                                                    }
                                                    placeholder={t("leaderboard.create.leagueNamePlaceholder")}
                                                    className="w-full"
                                                />
                                                {createErrors.name && (
                                                    <p className="text-red-500 text-xs mt-1">
                                                        {createErrors.name}
                                                    </p>
                                                )}
                                            </div>

                                            {/* League Type */}
                                            <div className="space-y-2">
                                                <Label htmlFor="type" className="text-sm font-medium">
                                                    {t("leaderboard.create.leagueType")}
                                                    <span className="text-red-500 ml-1">*</span>
                                                </Label>
                                                <Select
                                                    value={createData.type}
                                                    onValueChange={(val) =>
                                                        setCreateData("type", val)
                                                    }
                                                >
                                                    <SelectTrigger className="w-full">
                                                        <SelectValue
                                                            placeholder={t("leaderboard.create.leagueTypePlaceholder")}
                                                        />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="private">
                                                            <div className="flex items-center gap-2">
                                                                <span className="w-2 h-2 rounded-full bg-amber-500"></span>
                                                                {t("leaderboard.create.privateLabel")}
                                                            </div>
                                                        </SelectItem>
                                                        <SelectItem value="public">
                                                            <div className="flex items-center gap-2">
                                                                <span className="w-2 h-2 rounded-full bg-emerald-500"></span>
                                                                {t("leaderboard.create.publicLabel")}
                                                            </div>
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <p className="text-xs text-muted-foreground">
                                                    {createData.type === "private"
                                                        ? t("leaderboard.create.privateDescription")
                                                        : t("leaderboard.create.publicDescription")}
                                                </p>
                                            </div>
                                        </div>

                                        {/* Right Column - Logo Upload */}
                                        <div className="space-y-6">
                                            <div className="space-y-2">
                                                <Label className="text-sm font-medium">
                                                    {t("leaderboard.create.leagueLogo")}
                                                </Label>

                                                <div className="flex flex-col items-center gap-4">
                                                    {/* Logo Preview */}
                                                    <div className="relative group">
                                                        <div className="w-32 h-32 rounded-full bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center overflow-hidden">
                                                            {createData.logoPreview ? (
                                                                <img
                                                                    src={createData.logoPreview}
                                                                    alt="Logo Preview"
                                                                    className="w-full h-full object-cover"
                                                                />
                                                            ) : (
                                                                <Camera className="w-8 h-8 text-gray-400" />
                                                            )}
                                                        </div>
                                                    </div>

                                                    {/* Upload Button */}
                                                    <div className="w-full">
                                                        <Label
                                                            htmlFor="logo-upload"
                                                            className="flex flex-col items-center justify-center w-full py-3 px-4 border border-gray-300 border-dashed rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                                                        >
                                                            <div className="flex items-center gap-2">
                                                                <Upload className="w-4 h-4" />
                                                                <span className="text-sm font-medium">
                                                                    {t("leaderboard.create.uploadLogo")}
                                                                </span>
                                                            </div>
                                                            <span className="text-xs text-muted-foreground mt-1">
                                                                {t("leaderboard.create.logoRequirements")}
                                                            </span>
                                                        </Label>
                                                        <Input
                                                            id="logo-upload"
                                                            type="file"
                                                            onChange={handleLogoChange}
                                                            className="hidden"
                                                            accept="image/*"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Submit Button */}
                                    <div className="pt-4">
                                        <Button
                                            type="submit"
                                            className="w-full py-6 text-lg font-semibold"
                                            disabled={createProcessing}
                                        >
                                            {createProcessing ? (
                                                <span className="flex items-center gap-2">
                                                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    {t("leaderboard.create.creating")}
                                                </span>
                                            ) : (
                                                t("leaderboard.create.createButton")
                                            )}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </Tabs.Content>
                </Tabs.Root>
            </div>
        </Layout>
    );
}