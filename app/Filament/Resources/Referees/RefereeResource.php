<?php

namespace App\Filament\Resources\Referees;

use App\Filament\Resources\Referees\Pages\CreateReferee;
use App\Filament\Resources\Referees\Pages\EditReferee;
use App\Filament\Resources\Referees\Pages\ListReferees;
use App\Filament\Resources\Referees\Pages\ViewReferee;
use App\Filament\Resources\Referees\Schemas\RefereeForm;
use App\Filament\Resources\Referees\Schemas\RefereeInfolist;
use App\Filament\Resources\Referees\Tables\RefereesTable;
use App\Models\Referee;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class RefereeResource extends Resource
{
    protected static ?string $model = Referee::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::UserGroup;

    protected static string|UnitEnum|null $navigationGroup = 'Competitions management';

    protected static ?int $navigationSort = 7;

    public static function form(Schema $schema): Schema
    {
        return RefereeForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return RefereeInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return RefereesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListReferees::route('/'),
            'create' => CreateReferee::route('/create'),
            'view' => ViewReferee::route('/{record}'),
            'edit' => EditReferee::route('/{record}/edit'),
        ];
    }
}
