<?php

namespace App\Filament\Resources\Referees\Schemas;

use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class RefereeInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema->schema([
            Group::make()
                ->schema([
                    Section::make('Personal Information')
                        ->schema([
                            TextEntry::make('name')
                                ->label('Name'),

                            TextEntry::make('name_ar')
                                ->label('Name (Arabic)'),

                            TextEntry::make('birthday')
                                ->date('d/m/Y'),

                            TextEntry::make('country')
                                ->label('Country'),
                        ])
                        ->columns(2),

                    Section::make('Professional Details')
                        ->schema([
                            TextEntry::make('license_number')
                                ->label('License Number'),

                            TextEntry::make('experience_years')
                                ->label('Experience (years)')
                                ->numeric(),
                        ])
                        ->columns(2),
                ])
                ->columnSpan(2),

            Group::make()
                ->schema([
                    ImageEntry::make('image')
                        ->label('Photo')
                        ->circular(),

                    Grid::make(2)
                        ->schema([
                            TextEntry::make('created_at')
                                ->dateTime(),

                            TextEntry::make('updated_at')
                                ->dateTime(),
                        ]),
                ])
                ->columnSpan(1),
        ])
            ->columns(3);
    }
}
