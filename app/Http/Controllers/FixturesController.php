<?php

namespace App\Http\Controllers;

use App\Services\FantasyContextService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class FixturesController extends Controller
{
    protected $fantasyContextService;

    public function __construct(FantasyContextService $fantasyContextService)
    {
        $this->fantasyContextService = $fantasyContextService;
    }

    /**
     * Display the fixtures page with dynamic data
     */
    public function index(Request $request): Response
    {
        $contextResult = $this->fantasyContextService->getFantasyContextWithErrors($request);

        if (! $contextResult['success']) {
            return Inertia::render('Fixtures', [
                'error' => $contextResult['error'],
                'fixtures' => [],
                'gameweeks' => [],
                'currentGameweek' => null,
            ]);
        }

        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];
        $currentSeasonPhase = $context['currentSeasonPhase'];
        // Get all gameweeks for the current season phase
        $gameweeks = $currentSeasonPhase->gameweeks()
            ->orderBy('start_date')
            ->get(['id', 'name', 'start_date', 'end_date', 'status']);

        // Get fixtures for the current gameweek and nearby gameweeks
        $gameweekIds = $gameweeks->pluck('id')->toArray();

        $fixtures = [];
        foreach ($gameweeks as $gameweek) {
            $games = $gameweek->games()
                ->with(['homeTeam:id,name,short_name,logo', 'awayTeam:id,name,short_name,logo'])
                ->orderBy('game_date')
                ->get([
                    'id',
                    'gameweek_id',
                    'home_team_id',
                    'away_team_id',
                    'game_date',
                    'home_score',
                    'away_score',
                    'status',
                ]);
            $fixtures[$gameweek->id] = $games->map(function ($game) {
                return [
                    'id' => $game->id,
                    'homeTeam' => [
                        'id' => $game->homeTeam->id,
                        'name' => $game->homeTeam->name,
                        'shortName' => $game->homeTeam->short_name,
                        'logo' => $game->homeTeam->logo,
                    ],
                    'awayTeam' => [
                        'id' => $game->awayTeam->id,
                        'name' => $game->awayTeam->name,
                        'shortName' => $game->awayTeam->short_name,
                        'logo' => $game->awayTeam->logo,
                    ],
                    'homeScore' => $game->home_score,
                    'awayScore' => $game->away_score,
                    'kickoff' => $game->game_date,
                    'status' => $game->status,
                ];
            });
        }

        return Inertia::render('Fixtures', [
            'fixtures' => $fixtures,
            'gameweeks' => $gameweeks->map(function ($gameweek) {
                return [
                    'id' => $gameweek->id,
                    'name' => $gameweek->name,
                    'startDate' => $gameweek->start_date,
                    'endDate' => $gameweek->end_date,
                    'status' => $gameweek->status,
                ];
            }),
            'currentGameweek' => [
                'id' => $currentGameweek->id,
                'name' => $currentGameweek->name,
                'startDate' => $currentGameweek->start_date,
                'endDate' => $currentGameweek->end_date,
                'status' => $currentGameweek->status,
            ],
            'competition' => [
                'id' => $context['competition']->id,
                'name' => $context['competition']->name,
            ],
            'season' => [
                'id' => $context['currentSeason']->id,
                'name' => $context['currentSeason']->name,
            ],
        ]);
    }
}
