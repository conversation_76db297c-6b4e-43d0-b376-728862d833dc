<?php

namespace App\Http\Controllers;

use App\Models\Competition;
use App\Models\FantasyTeam;
use App\Models\League;
use App\Models\RankingLeague;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Inertia\Inertia;

class LeagueController extends Controller
{
    public function index()
    {
        $competitionId = session('current_competition_id');
        $user = Auth::user();

        // Get current competition with its current season
        $competition = Competition::with('currentSeason')->find($competitionId);
        if (! $competition || ! $competition->currentSeason) {
            return redirect()->back()->with('error', 'No active competition or season found.');
        }
        $seasonId = $competition->currentSeason->id;

        // Get user's fantasy teams for current season
        $userFantasyTeams = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $seasonId)
            ->get();
        // Get leagues the user is part of
        $myLeagues = League::where('season_id', $seasonId)
            ->whereHas('fantasyTeams', function ($query) use ($userFantasyTeams) {
                $query->whereIn('fantasy_team_id', $userFantasyTeams->pluck('id'));
            })
            ->with(['owner', 'fantasyTeams'])
            ->get()
            ->map(function ($league) use ($userFantasyTeams) {
                // Get user's ranking in this league
                $userTeamId = $userFantasyTeams->first()?->id;
                $ranking = RankingLeague::where('league_id', $league->id)
                    ->where('fantasy_team_id', $userTeamId)
                    ->orderBy('gameweek_id', 'desc')
                    ->first();

                return [
                    'id' => $league->id,
                    'name' => $league->name,
                    'type' => ucfirst($league->type),
                    'members' => $league->fantasyTeams->count(),
                    'rank' => $ranking?->rank ?? 'N/A',
                    'points' => $ranking?->points ?? 0,
                    'logo' => $league->logo,
                ];
            });

        $availableLeagues = League::where('season_id', $seasonId)
            ->where('type', 'public')
            ->whereDoesntHave('fantasyTeams', function ($query) use ($userFantasyTeams) {
                $query->whereIn('fantasy_team_id', $userFantasyTeams->pluck('id'));
            })
            ->with(['owner', 'fantasyTeams'])
            ->get()
            ->map(function ($league) {
                return [
                    'id' => $league->id,
                    'name' => $league->name,
                    'type' => ucfirst($league->type),
                    'members' => $league->fantasyTeams->count(),
                    'entryFee' => 'Free',
                    'prize' => 'TBD',
                    'logo' => $league->logo,
                ];
            });

        return Inertia::render('Leagues', [
            'myLeagues' => $myLeagues,
            'availableLeagues' => $availableLeagues,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:public,private',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $competitionId = session('current_competition_id');
        $user = Auth::user();

        // Ensure we have a valid competition ID
        if (! $competitionId) {
            return redirect()->back()->with('error', 'No active competition selected. Please select a competition first.');
        }

        // Get current competition with its current season
        $competition = Competition::with('currentSeason')->find($competitionId);
        if (! $competition || ! $competition->currentSeason) {
            return redirect()->back()->with('error', 'No active competition or season found.');
        }

        $seasonId = $competition->currentSeason->id;

        // Handle logo upload
        $logoPath = null;
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('leagues/logos', 'public');
        }

        $league = League::create([
            'name' => $validated['name'],
            'season_id' => $seasonId,
            'owner_id' => $user->id, // Added missing owner_id
            'type' => $validated['type'],
            'invite_code' => $validated['type'] === 'private' ? Str::upper(Str::random(8)) : null,
            'logo' => $logoPath,
        ]);

        // Automatically join the creator's fantasy team to the league
        $userFantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $seasonId)
            ->first();

        if ($userFantasyTeam) {
            // Add the creator's fantasy team as a member of the league
            $league->fantasyTeams()->attach($userFantasyTeam->id);

            $successMessage = 'League created successfully! You have been automatically added as a member.';
        } else {
            // If no fantasy team exists, still create the league but warn the user
            $successMessage = 'League created successfully! Please create a fantasy team to join as a member.';
        }

        return redirect()->route('game.leagues')->with('success', $successMessage);
    }

    public function join(Request $request, League $league)
    {
        $user = Auth::user();
        $competitionId = session('current_competition_id');

        // Get current competition with its current season
        $competition = Competition::with('currentSeason')->find($competitionId);
        if (! $competition || ! $competition->currentSeason) {
            return redirect()->back()->with('error', 'No active competition or season found.');
        }

        $seasonId = $competition->currentSeason->id;

        $userFantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $seasonId)
            ->first();

        if (! $userFantasyTeam) {
            return redirect()->back()->with('error', 'You need to create a fantasy team first!');
        }

        // Check if already joined
        if ($league->fantasyTeams()->where('fantasy_team_id', $userFantasyTeam->id)->exists()) {
            return redirect()->back()->with('error', 'You are already a member of this league!');
        }

        $league->fantasyTeams()->attach($userFantasyTeam->id);

        return redirect()->route('game.leagues')->with('success', 'Successfully joined the league!');
    }

    public function joinByCode(Request $request)
    {
        $request->validate([
            'invite_code' => 'required|string',
        ]);
        $league = League::where('invite_code', $request->invite_code)
            ->where('type', 'private')
            ->first();
        if (! $league) {
            return redirect()->back()->with('error', 'Invalid invite code!');
        }

        return $this->join($request, $league);
    }

    public function ranking(Request $request, League $league)
    {
        $season = $league->season;
        $phases = $season->seasonPhases()->orderBy('id')->get();

        $selectedPhaseId = $request->input('phase_id', $phases->first()?->id ?? null);
        $selectedPhase = $phases->where('id', $selectedPhaseId)->first();

        $gameweeks = $selectedPhase ? $selectedPhase->gameweeks()->orderBy('name')->get() : collect();
        $selectedGameweekId = $request->input('gameweek_id', $gameweeks->first()?->id ?? null);

        if ($selectedGameweekId) {
            $currentGameweek = $gameweeks->where('id', $selectedGameweekId)->first();
            $previousGameweek = $gameweeks->where('id', '<', $currentGameweek->id)->sortByDesc('id')->first();

            $previousRankings = $previousGameweek
                ? RankingLeague::where('league_id', $league->id)
                    ->where('gameweek_id', $previousGameweek->id)
                    ->get()
                    ->keyBy('fantasy_team_id')
                : collect();

            $currentRankings = RankingLeague::where('league_id', $league->id)
                ->where('gameweek_id', $selectedGameweekId)
                ->with(['fantasyTeam.user'])
                ->orderBy('rank', 'asc')
                ->paginate(20);

            $standingsData = $currentRankings->getCollection()->map(function ($ranking) use ($previousRankings) {
                $previousRank = $previousRankings->get($ranking->fantasy_team_id);

                return [
                    'rank' => $ranking->rank,
                    'previous_rank' => $previousRank->rank ?? null,
                    'name' => $ranking->fantasyTeam->user->first_name,
                    'teamName' => $ranking->fantasyTeam->name,
                    'points' => $ranking->points,
                ];
            });

            $standings = new \Illuminate\Pagination\LengthAwarePaginator(
                $standingsData,
                $currentRankings->total(),
                $currentRankings->perPage(),
                $currentRankings->currentPage(),
                ['path' => $request->url(), 'query' => $request->query()]
            );
        } else {
            // Create an empty paginator if no gameweek is selected
            $standings = new \Illuminate\Pagination\LengthAwarePaginator(
                collect(),
                0,
                20,
                1,
                ['path' => $request->url(), 'query' => $request->query()]
            );
        }

        return Inertia::render('Leagues/Ranking', [
            'league' => $league->load(['fantasyTeams.user', 'season', 'season.currentSeasonPhase', 'season.currentGameweek']),
            'standings' => $standings,
            'seasons' => [['id' => (string) $season->id, 'name' => (string) $season->name]],
            'phases' => $phases->map(fn ($p) => ['id' => (string) $p->id, 'name' => $p->name]),
            'gameweeks' => $gameweeks->map(fn ($g) => ['id' => (string) $g->id, 'name' => (string) $g->name]),
            'selectedSeasonId' => (string) $season->id,
            'selectedPhaseId' => (string) $selectedPhaseId,
            'selectedGameweekId' => (string) $selectedGameweekId,
        ]);
    }

    public function show(League $league)
    {
        $standings = RankingLeague::where('league_id', $league->id)
            ->with(['fantasyTeam.user'])
            ->whereHas('gameweek', function ($query) {
                $query->orderBy('id', 'desc');
            })
            ->orderBy('rank')
            ->get()
            ->map(function ($ranking) {
                return [
                    'rank' => $ranking->rank,
                    'name' => $ranking->fantasyTeam->user->name,
                    'teamName' => $ranking->fantasyTeam->name,
                    'points' => $ranking->points,
                    'lastWeek' => $ranking->rank_gameweek,
                ];
            });

        return Inertia::render('LeagueDetail', [
            'league' => $league->load(['owner', 'fantasyTeams.user']),
            'standings' => $standings,
        ]);
    }

    public function destroy(League $league)
    {
        $user = Auth::user();
        $competitionId = session('current_competition_id');
        $competition = Competition::with('currentSeason')->find($competitionId);
        if (! $competition || ! $competition->currentSeason) {
            return redirect()->back()->with('error', 'No active competition or season found.');
        }

        $seasonId = $competition->currentSeason->id;

        $userFantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $seasonId)
            ->first();
        if ($user->id === $league->owner_id) {
            League::find($league->id)->delete();
        }
        if ($userFantasyTeam) {
            $league->fantasyTeams()->detach($userFantasyTeam->id);
        }

        return redirect()->route('game.leagues')->with('success', 'You have left the league.');
    }
}
