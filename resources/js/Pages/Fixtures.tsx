import React, { useState } from "react";
import { Head } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/Components/ui/card";
import * as Tabs from "@radix-ui/react-tabs";
import { PageProps } from "../types/inertia";
import HeroSection from "../Components/ui/HeroSection";
import FixtureItem from "../Components/fantasy/FixtureItem";
import { useTenantName } from "@/hooks/useTenant";
import { useTranslation } from "react-i18next";

interface Gameweek {
    id: number;
    name: string;
    startDate: string;
    endDate: string;
    status: string;
}

interface FixturesProps extends PageProps {
    fixtures: Record<number, any[]>;
    gameweeks: Gameweek[];
    currentGameweek: Gameweek | null;
    competition: {
        id: number;
        name: string;
    };
    season: {
        id: number;
        name: string;
    };
    error?: string;
}

export default function Fixtures(props: FixturesProps) {
    const tenantName = useTenantName();
    const { t, i18n } = useTranslation();
    const { fixtures, gameweeks, currentGameweek, competition, season, error } =
        props;

    const [selectedGameweek, setSelectedGameweek] = useState(
        currentGameweek?.id || (gameweeks.length > 0 ? gameweeks[0].id : 1)
    );

    // Handle error state
    if (error) {
        return (
            <Layout {...props}>
                <Head title={t('fixtures.title')} />
                <div className="container mx-auto px-4 py-8">
                    <div className="text-center">
                        <h1 className="text-2xl font-bold text-red-600 mb-4">
                            {t('fixtures.errorLoading')}
                        </h1>
                        <p className="text-gray-600">{error}</p>
                    </div>
                </div>
            </Layout>
        );
    }

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString(i18n.language, {
            weekday: "short",
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });
    };
   
    return (
        <Layout {...props}>
            <Head title={`${tenantName} - ${t('fixtures.title')}`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title={t('fixtures.title')}
                    subtitle={t('fixtures.viewMatches') + 
                        competition?.name + ' - ' +
                        season?.name
                    }
                />

                {/* Gameweek Selector */}
                <div className="flex justify-center">
                    <Tabs.Root
                        value={selectedGameweek.toString()}
                        onValueChange={(value: string) =>
                            setSelectedGameweek(parseInt(value))
                        }
                    >
                        <Tabs.List
                            className={`grid w-full bg-slate-100 rounded-lg p-1`}
                            style={{
                                gridTemplateColumns: `repeat(${Math.min(
                                    gameweeks.length,
                                    5
                                )}, 1fr)`,
                            }}
                        >
                            {gameweeks.slice(0, 5).map((gameweek) => (
                                <Tabs.Trigger
                                    key={gameweek.id}
                                    value={gameweek.id.toString()}
                                    className="px-4 py-2 rounded-md font-medium transition-all data-[state=active]:bg-white data-[state=active]:text-slate-900 data-[state=active]:shadow-sm text-slate-600 hover:text-slate-900 text-sm"
                                >
                                    {gameweek.name}
                                    {currentGameweek?.id === gameweek.id &&
                                        ` (${t('fixtures.current')})`}
                                </Tabs.Trigger>
                            ))}
                        </Tabs.List>
                    </Tabs.Root>
                </div>

                {/* Fixtures Grid */}
                <div className="w-full">
                    <Card className="transparent-card">
                        <CardHeader>
                            <CardTitle className="text-gray-200">
                                {gameweeks.find(
                                    (gw) => gw.id === selectedGameweek
                                )?.name || t('fixtures.gameweek', { number: selectedGameweek })}{' '}
                                {t('fixtures.title')}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {(fixtures[selectedGameweek] || []).length === 0 ? (
                                <div className="text-center py-8 text-gray-400">
                                    {t('fixtures.noFixtures')}
                                </div>
                            ) : (
                                (fixtures[selectedGameweek] || []).map(
                                    (fixture: any) => (
                                        <FixtureItem
                                            key={fixture.id}
                                            homeTeam={fixture.homeTeam.name}
                                            awayTeam={fixture.awayTeam.name}
                                            kickoff={fixture.kickoff}
                                            homeScore={fixture.homeScore}
                                            awayScore={fixture.awayScore}
                                            status={
                                                fixture.status as
                                                    | "upcoming"
                                                    | "finished"
                                                    | "live"
                                            }
                                            gameweek={
                                                gameweeks.find(
                                                    (gw) =>
                                                        gw.id ===
                                                        selectedGameweek
                                                )?.name ||
                                                t('fixtures.gameweek', { number: selectedGameweek })
                                            }
                                        />
                                    )
                                )
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </Layout>
    );
}
