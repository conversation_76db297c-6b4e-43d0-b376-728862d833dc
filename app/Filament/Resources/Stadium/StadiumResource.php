<?php

namespace App\Filament\Resources\Stadium;

use App\Enums\FantasyIcon;
use App\Filament\Resources\Stadium\Pages\CreateStadium;
use App\Filament\Resources\Stadium\Pages\EditStadium;
use App\Filament\Resources\Stadium\Pages\ListStadium;
use App\Filament\Resources\Stadium\Pages\ViewStadium;
use App\Filament\Resources\Stadium\Schemas\StadiumForm;
use App\Filament\Resources\Stadium\Schemas\StadiumInfolist;
use App\Filament\Resources\Stadium\Tables\StadiumTable;
use App\Models\Stadium;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;
use UnitEnum;

class StadiumResource extends Resource
{
    protected static ?string $model = Stadium::class;

    protected static string|BackedEnum|null $navigationIcon = FantasyIcon::Stadium;

    protected static ?int $navigationSort = 5;

    public static function getModelLabel(): string
    {
        return 'Stadiums';
    }

    protected static string|UnitEnum|null $navigationGroup = 'Competitions management';

    public static function form(Schema $schema): Schema
    {
        return StadiumForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return StadiumInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return StadiumTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            \App\Filament\Resources\Stadium\RelationManagers\TeamsRelationManager::class,
            \App\Filament\Resources\Stadium\RelationManagers\GamesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListStadium::route('/'),
            'create' => CreateStadium::route('/create'),
            'view' => ViewStadium::route('/{record}'),
            'edit' => EditStadium::route('/{record}/edit'),
        ];
    }
}
