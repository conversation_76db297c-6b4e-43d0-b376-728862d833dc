import React, { FC } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";

const MarketPlayer: FC<{
    player: TransferPlayer;
    onPlayerClick: (player: TransferPlayer) => void;
    onInfoClick: (player: TransferPlayer) => void;
    isEligible: boolean;
    isInSquad: boolean;
}> = ({ player, onPlayerClick, onInfoClick, isEligible, isInSquad }) => {
    const { t } = useTranslation();
    return (
        <tr
            onClick={() => isEligible && !isInSquad && onPlayerClick(player)}
            className={`border-b border-gray-700 ${
                isInSquad
                    ? "bg-gray-800 text-gray-500"
                    : isEligible
                    ? "bg-transparent cursor-pointer"
                    : "bg-gray-900 text-gray-500"
            }`}
        >
            <td className="p-2 flex items-center space-x-2">
                <button
                    onClick={(e) => {
                        e.stopPropagation();
                        onInfoClick(player);
                    }}
                    className="bg-blue-500 text-white rounded-full h-5 w-5 flex-shrink-0 flex items-center justify-center text-xs font-bold hover:bg-blue-700"
                    aria-label={t("transferManager.playerActions.info", {
                        name: player.name,
                    })}
                >
                    i
                </button>
                <span>{player.name}</span>
            </td>
            <td className="p-2">{player.team}</td>
            <td className="p-2">{player.position}</td>
            <td className="p-2 text-right">{player.price.toFixed(1)}</td>
        </tr>
    );
};

export default MarketPlayer;