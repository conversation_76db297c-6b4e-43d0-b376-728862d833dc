import { createRoot } from "react-dom/client";
import { createInertiaApp } from "@inertiajs/react";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { PageProps } from "@/types/inertia";
import i18n from "./i18n";

const appName = import.meta.env.VITE_APP_NAME || "Fantasy Football";

createInertiaApp<PageProps>({
    title: (title) => `${title} - ${appName}`,
    progress: {
        color: "#16a34a",
        showSpinner: false,
        includeCSS: true,
    },
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.tsx`,
            import.meta.glob("./Pages/**/*.tsx")
        ),
    setup({ el, App, props }) {
        (async () => {
            const { locale, available_languages, translations } =
                props.initialPage.props.app;

            await i18n.init({
                lng: locale,
                fallbackLng: "fr",
                supportedLngs: available_languages,
                debug: false, // Enable debug logging
                ns: ["translation"], // Define namespaces
                defaultNS: "translation", // Set default namespace
                fallbackNS: "translation", // Fallback namespace
                resources: {
                    [locale]: {
                        translation: translations,
                    },
                },
                interpolation: {
                    escapeValue: false, // react already safes from xss
                },
                keySeparator: ".", // Enable nested key access
                nsSeparator: false, // Disable namespace separator since we're using single file
            });

            const root = createRoot(el);
            root.render(<App {...props} />);
        })();
    },
});
