import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/Components/ui/card';

export default function LeagueNews() {
    return (
        <div className="lg:col-span-2">
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        📰 League News
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <div className="p-3 bg-muted/50 rounded-lg">
                            <p className="text-sm font-medium">Latest Update</p>
                            <p className="text-xs text-muted-foreground mt-1">
                                New gameweek started! Check your lineup.
                            </p>
                        </div>
                        <div className="p-3 bg-muted/50 rounded-lg">
                            <p className="text-sm font-medium">Transfer Window</p>
                            <p className="text-xs text-muted-foreground mt-1">
                                Make your transfers before the deadline.
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}