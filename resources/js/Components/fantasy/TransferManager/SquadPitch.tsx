import React, { FC } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";
import PitchPlayer from "./PitchPlayer";
import { squadLayout } from "@/lib/fantasy/transfer-management";

const SquadPitch: FC<{
    players: TransferPlayer[];
    onPlayerClick: (player: TransferPlayer) => void;
    onInfoClick: (player: TransferPlayer) => void;
    selectedPlayerId: number | string | null;
}> = ({ players, onPlayerClick, onInfoClick, selectedPlayerId }) => {
    const { t } = useTranslation();
    const positionCounters = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
    return (
        <div className="transparent-card p-4 rounded-lg w-full lg:w-1/2">
            <h2 className="text-white text-xl font-bold mb-4 text-center">
                {t("transferManager.titles.squad")}
            </h2>
            <div className="fantasy-pitch">
                {players.map((player) => {
                    const posType = player.position;
                    const posIndex = positionCounters[posType];
                    const pitchPosition = squadLayout[posType][posIndex];
                    positionCounters[posType]++;
                    return (
                        <PitchPlayer
                            key={player.id}
                            player={player}
                            position={pitchPosition}
                            onClick={onPlayerClick}
                            onInfoClick={onInfoClick}
                            isSelected={player.id === selectedPlayerId}
                        />
                    );
                })}
            </div>
        </div>
    );
};

export default SquadPitch;
