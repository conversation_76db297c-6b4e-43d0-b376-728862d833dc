<?php

namespace App\Http\Middleware;

use App\Enums\PlayerPosition;
use App\Services\FantasyContextService;
use App\Services\PlayerDataService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureValidFantasySquad
{
    public function __construct(
        protected FantasyContextService $fantasyContextService,
        protected PlayerDataService $playerDataService
    ) {
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip middleware for fantasy team creation/squad setup routes to prevent redirect loop
        if ($request->routeIs('fantasy-team.create') ||
            $request->routeIs('fantasy-team.store') ||
            $request->routeIs('fantasy-team.squad') ||
            $request->routeIs('fantasy-team.squad.store') ||
            $request->routeIs('competition.set-current')) {
            return $next($request);
        }

        $user = Auth::user();

        // If no user, let other middleware handle this
        if (! $user) {
            return $next($request);
        }

        $fantasyTeam = $this->fantasyContextService->getUserFantasyTeam($request);

        if (! $fantasyTeam) {
            // This should be handled by EnsureFantasyTeamExists middleware, so just pass through.
            return $next($request);
        }

        // We still need the gameweek, which is part of the competition context.
        $competition = $this->fantasyContextService->getCurrentCompetition($request);
        $currentGameweek = $competition?->currentSeason?->currentSeasonPhase?->currentGameweek ?? null;

        if (! $currentGameweek) {
            return $next($request);
        }

        // Check if the fantasy team has a valid squad for the current gameweek
        $squadPlayers = $this->playerDataService->getSquadPlayers($fantasyTeam->id, $currentGameweek);

        // Validate squad composition
        if (! $this->isValidSquad($squadPlayers)) {
            return redirect()->route('fantasy-team.squad');
        }

        return $next($request);
    }

    /**
     * Validate if the squad has the correct composition
     * Required: 15 players total (2 GK, 5 DEF, 5 MID, 3 FWD) with 11 starting and 4 on bench
     */
    private function isValidSquad($squadPlayers): bool
    {
        if ($squadPlayers->count() !== 15) {
            return false;
        }

        // Count players by position using enum values
        $positionCounts = array_fill_keys(PlayerPosition::values(), 0);
        $startingCount = 0;
        $benchCount = 0;
        $unassignedCount = 0;

        foreach ($squadPlayers as $fantasyPlayer) {
            $position = $fantasyPlayer->player->position;

            // Position is already cast to PlayerPosition enum
            if ($position instanceof PlayerPosition) {
                $positionCounts[$position->value]++;
            }

            // Check player's lineup status
            $lineupEntries = $fantasyPlayer->fantasyTeamLineups;

            if ($lineupEntries->isEmpty()) {
                // No lineup entry - player is unassigned
                $unassignedCount++;
            } else {
                // Check if player is starting or on bench based on position field
                $lineupEntry = $lineupEntries->first();
                if ($lineupEntry && $lineupEntry->position === 'starting') {
                    $startingCount++;
                } elseif ($lineupEntry && $lineupEntry->position === 'bench') {
                    $benchCount++;
                } else {
                    // Unknown position, treat as unassigned
                    $unassignedCount++;
                }
            }
        }

        // Validate position requirements using enum requirements
        $requirements = PlayerPosition::requirements();
        foreach ($requirements as $position => $requiredCount) {
            if ($positionCounts[$position] !== $requiredCount) {
                return false;
            }
        }

        // Validate lineup composition: exactly 11 starting, 4 on bench, 0 unassigned
        return $startingCount === 11 && $benchCount === 4 && $unassignedCount === 0;
    }
}
