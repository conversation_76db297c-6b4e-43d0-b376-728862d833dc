@php
    use Filament\Forms\Components\ColorPicker;
    use Filament\Forms\Components\Select;

    $statePath = $getStatePath();
    $svgContent = file_get_contents(resource_path('js/Components/shirt-01.svg'));
@endphp

<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div x-data="shirtCreator({
            state: $wire.entangle('{{ $statePath }}'),
            logo: '/storage/{{ $get('logo') }}'
        })" class="space-y-6">

        <template x-ref="shirtTemplate" style="display: none;">{!! $svgContent !!}</template>

        <!-- Shirt Preview Section -->
        <div class="fi-fo-field-wrp">
            <div class="fi-fo-field-wrp-label">
                <label class="fi-fo-field-wrp-label-text text-sm font-medium leading-6 text-gray-950 dark:text-white">
                    <span>Shirt Preview</span>
                </label>
            </div>
            <div
                class="fi-input-wrp flex rounded-lg shadow-sm ring-1 ring-gray-950/10 transition duration-75 bg-white dark:bg-white/5 dark:ring-white/20">
                <div
                    class="min-h-[250px] w-full p-6 flex items-center justify-center bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                    <div x-html="shirtPreview" class="w-full max-w-[200px] h-auto"></div>
                </div>
            </div>
        </div>

        <!-- Controls Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Base Color -->
            <div>
                <x-filament-forms::field-wrapper :field="
                        ColorPicker::make('base_color')
            ->label('Base Color')
            ->helperText('Primary shirt color')
            ->live()
            ->extraAlpineAttributes([
                'x-model' => 'state.base_color'
            ])
                    ">
                    <x-filament::input.wrapper>
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded border-2 border-gray-300 cursor-pointer"
                                :style="`background-color: ${state.base_color || '#ffffff'}`"
                                @click="$refs.baseColorInput.click()"></div>
                            <input x-ref="baseColorInput" type="color" x-model="state.base_color" class="sr-only">
                            <x-filament::input type="text" x-model="state.base_color" placeholder="#ffffff"
                                class="flex-1" />
                        </div>
                    </x-filament::input.wrapper>
                </x-filament-forms::field-wrapper>
            </div>

            <!-- Pattern Type -->
            <div>
                <x-filament-forms::field-wrapper :field="
                        Select::make('pattern_type')
            ->label('Pattern Type')
            ->helperText('Choose a pattern for the shirt')
            ->options([
                '' => 'None',
                'secondary_pattern_01' => 'Vertical Stripes',
                'secondary_pattern_02' => 'Horizontal Stripes',
                'secondary_pattern_03' => 'Diagonal Stripes',
                'secondary_pattern_04' => 'Chevron',
                'secondary_pattern_05' => 'Dots',
                'secondary_pattern_06' => 'Squares',
                'secondary_pattern_07' => 'Diamond',
                'secondary_pattern_08' => 'Gradient',
                'secondary_pattern_09' => 'Side Panel',
                'secondary_pattern_10' => 'Shoulder Panel',
                'secondary_pattern_11' => 'Custom Pattern',
            ])
            ->live()
            ->extraAlpineAttributes([
                'x-model' => 'state.pattern_type'
            ])
                    ">
                    <x-filament::input.wrapper>
                        <select x-model="state.pattern_type"
                            class="fi-select-input block w-full border-0 py-1.5 text-gray-900 dark:text-white bg-transparent focus:ring-0 sm:text-sm sm:leading-6">
                            <option value="">None</option>
                            <option value="secondary_pattern_01">Vertical Stripes</option>
                            <option value="secondary_pattern_02">Horizontal Stripes</option>
                            <option value="secondary_pattern_03">Diagonal Stripes</option>
                            <option value="secondary_pattern_04">Chevron</option>
                            <option value="secondary_pattern_05">Dots</option>
                            <option value="secondary_pattern_06">Squares</option>
                            <option value="secondary_pattern_07">Diamond</option>
                            <option value="secondary_pattern_08">Gradient</option>
                            <option value="secondary_pattern_09">Side Panel</option>
                            <option value="secondary_pattern_10">Shoulder Panel</option>
                            <option value="secondary_pattern_11">Custom Pattern</option>
                        </select>
                    </x-filament::input.wrapper>
                </x-filament-forms::field-wrapper>
            </div>
        </div>

        <!-- Pattern Color (conditional) -->
        <div x-show="state.pattern_type" x-transition>
            <x-filament-forms::field-wrapper :field="
                    ColorPicker::make('pattern_color')
            ->label('Pattern Color')
            ->helperText('Color for the selected pattern')
            ->live()
            ->extraAlpineAttributes([
                'x-model' => 'state.pattern_color'
            ])
                ">
                <x-filament::input.wrapper>
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 rounded border-2 border-gray-300 cursor-pointer"
                            :style="`background-color: ${state.pattern_color || '#000000'}`"
                            @click="$refs.patternColorInput.click()"></div>
                        <input x-ref="patternColorInput" type="color" x-model="state.pattern_color" class="sr-only">
                        <x-filament::input type="text" x-model="state.pattern_color" placeholder="#000000"
                            class="flex-1" />
                    </div>
                </x-filament::input.wrapper>
            </x-filament-forms::field-wrapper>
        </div>
    </div>

    @push('scripts')
        <script>
            function shirtCreator(config) {
                return {
                    state: config.state,
                    logo: config.logo,
                    shirtSvg: '',
                    init() {
                        this.shirtSvg = this.$refs.shirtTemplate.innerHTML;
                        if (typeof this.state !== 'object' || this.state === null) {
                            this.state = {
                                base_color: '#ffffff',
                                pattern_type: '',
                                pattern_color: '#000000'
                            };
                        }
                    },
                    get shirtPreview() {
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(this.shirtSvg, 'image/svg+xml');
                        const base = doc.querySelector('#base-color');
                        if (base && this.state && this.state.base_color) {
                            base.setAttribute('fill', this.state.base_color);
                            base.querySelectorAll('path').forEach(p => p.setAttribute('style', `fill: ${this.state.base_color}`));
                        }

                        const logoElement = doc.querySelector('#club_logo image');
                        if (logoElement && this.logo) {
                            console.log(this.logo);
                            logoElement.setAttribute('xlink:href', this.logo);
                        }

                        // Hide all secondary patterns at once
                        const allPatternParts = doc.querySelectorAll(`[id^="secondary_pattern_"]`);
                        allPatternParts.forEach(part => part.style.display = 'none');

                        // Show and color the selected pattern
                        if (this.state && this.state.pattern_type) {
                            const patternParts = doc.querySelectorAll(`[id^="${this.state.pattern_type}"]`);
                            patternParts.forEach(part => {
                                part.style.display = 'block';
                                if (this.state.pattern_color) {
                                    part.setAttribute('fill', this.state.pattern_color);
                                    part.querySelectorAll('path').forEach(p => p.setAttribute('style', `fill: ${this.state.pattern_color}`));
                                }
                            });
                        }

                        return new XMLSerializer().serializeToString(doc.documentElement);
                    }
                }
            }
        </script>
    @endpush
</x-dynamic-component>