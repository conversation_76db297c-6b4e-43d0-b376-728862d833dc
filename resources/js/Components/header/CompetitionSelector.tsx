import { router } from "@inertiajs/react";
import { usePage } from "@inertiajs/react";
import { PageProps } from "@/types/inertia";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/Components/ui/select";

export default function CompetitionSelector() {
    const { competitions, currentCompetition } = usePage<PageProps>().props;

    // Don't show the selector if there's only one competition or no competitions
    if (!competitions || competitions.length <= 1) {
        return null;
    }

    const handleCompetitionChange = (competitionId: string) => {
        const competition = competitions.find(
            (c) => c.id.toString() === competitionId
        );
        if (competition) {
            router.post(
                route("competition.set-current", competition.id),
                {},
                {
                    preserveState: true,
                    preserveScroll: true,
                }
            );
        }
    };

    return (
        <div className="flex items-center space-x-2">
            <Select
                value={currentCompetition?.id.toString() || ""}
                onValueChange={handleCompetitionChange}
            >
                <SelectTrigger className="w-[200px] h-8 text-sm">
                    <SelectValue placeholder="Select competition" />
                </SelectTrigger>
                <SelectContent>
                    {competitions.map((competition) => (
                        <SelectItem
                            key={competition.id}
                            value={competition.id.toString()}
                        >
                            {competition.name}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
}
