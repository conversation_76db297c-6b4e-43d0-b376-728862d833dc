<?php

namespace App\Filament\Resources\Competitions\Schemas;

use App\Enums\CompetitionStatus;
use App\Enums\CompetitionType;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CompetitionForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->description('Configure the competition details and identification')
                    ->columns(2)
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Premier League')
                            ->columnSpan(1),
                        TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., premier-league')
                            ->helperText('URL-friendly version of the name')
                            ->columnSpan(1),
                    ]),

                Section::make('Branding')
                    ->description('Competition visual identity')
                    ->columns(1)
                    ->schema([
                        FileUpload::make('logo')
                            ->required()
                            ->label('Competition Logo')
                            ->image()
                            ->directory('competitions')
                            ->disk('public')
                            ->imageEditor()
                            ->imageEditorAspectRatios(['1:1'])
                            ->columnSpan('full'),
                    ]),

                Section::make('Configuration')
                    ->description('Competition settings and current status')
                    ->columns(2)
                    ->schema([
                        Select::make('type')
                            ->label('Competition Type')
                            ->options(CompetitionType::class)
                            ->native(false)
                            ->required()
                            ->helperText('Choose the format of this competition')
                            ->columnSpan(1),
                        Select::make('status')
                            ->label('Competition Status')
                            ->options(CompetitionStatus::class)
                            ->native(false)

                            ->required()
                            ->default(CompetitionStatus::UPCOMING)
                            ->helperText('Current status of the competition')
                            ->columnSpan(1),
                        Select::make('current_season_id')
                            ->label('Current Season')
                            ->relationship('currentSeason', 'name')
                            ->searchable()
                            ->preload()
                            ->nullable()
                            ->helperText('Select the active season for this competition')
                            ->columnSpan(2),
                    ]),
            ]);
    }
}
