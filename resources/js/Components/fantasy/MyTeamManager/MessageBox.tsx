import React, { <PERSON> } from "react";

const MessageBox: FC<{ message: string; type: "info" | "error" }> = ({
    message,
    type,
}) => {
    const baseClasses = "p-4 rounded-lg mb-4 text-center font-semibold";
    const typeClasses = {
        info: "bg-blue-900 text-blue-100",
        error: "bg-red-900 text-red-100",
    };
    return (
        <div className={`${baseClasses} ${typeClasses[type]}`}>{message}</div>
    );
};

export default MessageBox;